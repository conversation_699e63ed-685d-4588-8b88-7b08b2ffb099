import 'interpretations/base_interpretation.dart';
import 'interpretations/left_ventricle_interpretation.dart';
import 'interpretations/left_atrium_interpretation.dart';
import 'interpretations/aortic_root_interpretation.dart';
import 'interpretations/right_ventricle_interpretation.dart';
import 'interpretations/right_atrium_interpretation.dart';
import 'interpretations/pulmonary_artery_interpretation.dart';
import 'interpretations/inferior_vena_cava_interpretation.dart';
import 'interpretations/mitral_valve_interpretation.dart';
import 'interpretations/aortic_valve_interpretation.dart';
import 'interpretations/tricuspid_valve_interpretation.dart';
import 'interpretations/pulmonary_valve_interpretation.dart';
import 'interpretations/pericardium_interpretation.dart';
import 'interpretations/other_findings_interpretation.dart';
import 'interpretations/conclusion_interpretation.dart';
import '../models/interpretation_section.dart';

class ModularInterpretationService {
  final Map<String, BaseInterpretation> _interpretations = {
    'LEFT VENTRICLE': LeftVentricleInterpretation(),
    'LEFT ATRIUM': LeftAtriumInterpretation(),
    'RIGHT VENTRICLE': RightVentricleInterpretation(),
    'RIGHT ATRIUM': RightAtriumInterpretation(),
    'MITRAL VALVE': MitralValveInterpretation(),
    'AORTIC VALVE': AorticValveInterpretation(),
    'TRICUSPID VALVE': TricuspidValveInterpretation(),
    'PULMONARY VALVE': PulmonaryValveInterpretation(),
    'PULMONARY ARTERY': PulmonaryArteryInterpretation(),
    'AORTIC ROOT': AorticRootInterpretation(),
    'INFERIOR VENA CAVA': InferiorVenaCavaInterpretation(),
    'PERICARDIUM': PericardiumInterpretation(),
    'OTHER FINDINGS': OtherFindingsInterpretation(),
    'CONCLUSION': ConclusionInterpretation(),
  };

  List<InterpretationSection> generateInterpretationAsSections(
    Map<String, dynamic> patientData,
  ) {
    if (patientData.isEmpty) {
      return [
        InterpretationSection(
          title: 'Status',
          content: 'No data available for interpretation.',
        ),
      ];
    }

    final List<InterpretationSection> sections = [];

    for (final entry in _interpretations.entries) {
      final sectionName = entry.key;
      final interpreter = entry.value;

      // Special handling for conclusion to support bullet points
      if (sectionName == 'CONCLUSION' &&
          interpreter is ConclusionInterpretation) {
        final List<String> diagnoses = interpreter.generateDiagnosesList(
          patientData,
        );

        if (diagnoses.isEmpty) {
          sections.add(
            InterpretationSection(
              title: '$sectionName:',
              content: 'Normal cardiac structure and function.',
            ),
          );
        } else {
          sections.add(
            InterpretationSection.withBulletPoints(
              title: '$sectionName:',
              points: diagnoses,
            ),
          );
        }
      } else {
        final String sectionInterpretation = interpreter.generateInterpretation(
          patientData,
        );

        sections.add(
          InterpretationSection(
            title: '$sectionName:',
            content: sectionInterpretation,
          ),
        );
      }
    }

    return sections;
  }

  String generateInterpretation(Map<String, dynamic> patientData) {
    if (patientData.isEmpty) {
      return 'No data available for interpretation.';
    }

    final StringBuffer interpretation = StringBuffer();

    for (final entry in _interpretations.entries) {
      final sectionName = entry.key;
      final interpreter = entry.value;

      final String sectionInterpretation = interpreter.generateInterpretation(
        patientData,
      );

      final List<String> lines = sectionInterpretation.split('\n');

      if (lines.isNotEmpty) {
        interpretation.write('$sectionName: ${lines[0]}');

        if (lines.length > 1) {
          interpretation.writeln();
          final String indent = ' ' * (sectionName.length + 2);

          for (int i = 1; i < lines.length; i++) {
            if (lines[i].trim().isNotEmpty) {
              interpretation.writeln('$indent${lines[i]}');
            }
          }
        }

        interpretation.writeln();
      } else {
        interpretation.writeln('$sectionName: No data available.');

        interpretation.writeln();
      }
    }

    return interpretation.toString();
  }
}
