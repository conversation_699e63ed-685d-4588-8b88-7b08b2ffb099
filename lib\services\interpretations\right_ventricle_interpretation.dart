import 'base_interpretation.dart';

class RightVentricleInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Right ventricular assessment was not performed.';
    }

    final echoParams = data['echoParameters'] ?? {};

    final rvSize = echoParams['rvSize'] as String?;
    final rvBase = _parseDouble(echoParams['rvBase']);
    final rvMid = _parseDouble(echoParams['rvMid']);
    final rvLength = _parseDouble(echoParams['rvLength']);
    final rvWallThickness = _parseDouble(echoParams['rvWallThickness']);
    final tapse = _parseDouble(echoParams['tapse']);
    final ivsd = _parseDouble(echoParams['ivsd']);

    final StringBuffer rvParagraph = StringBuffer();

    if (rvSize != null) {
      if (rvSize == 'normal') {
        rvParagraph.write(' Is normal in size');
      } else if (rvSize == 'dilated') {
        rvParagraph.write(' Is dilated');

        if (rvBase != null || rvMid != null || rvLength != null) {
          rvParagraph.write(' with the following dimensions:');

          if (rvBase != null) {
            final String baseSize = _interpretRVBase(rvBase);
            rvParagraph.write(' basal diameter $rvBase mm ($baseSize)');

            if (rvMid != null || rvLength != null) {
              rvParagraph.write(',');
            }
          }

          if (rvMid != null) {
            final String midSize = _interpretRVMid(rvMid);
            rvParagraph.write(' mid diameter $rvMid mm ($midSize)');

            if (rvLength != null) {
              rvParagraph.write(',');
            }
          }

          if (rvLength != null) {
            final String lengthSize = _interpretRVLength(rvLength);
            rvParagraph.write(' length $rvLength mm ($lengthSize)');
          }
        }
      }
    } else {
      rvParagraph.write('Right ventricular size was not assessed');
    }

    if (rvWallThickness != null || ivsd != null) {
      if (rvParagraph.isNotEmpty) {
        rvParagraph.write('. ');
      }

      if (rvWallThickness != null) {
        final String wallThicknessInterpretation = _interpretRVWallThickness(
          rvWallThickness,
          ivsd,
        );
        rvParagraph.write(
          'RV free wall thickness is $rvWallThickness mm ($wallThicknessInterpretation)',
        );

        if (ivsd != null) {
          final String ivsdInterpretation = _interpretIVSD(ivsd);

          if (rvSize == 'dilated' && ivsd < 0.8) {
            rvParagraph.write(
              ' with interventricular septum thickness of $ivsd mm ($ivsdInterpretation), consistent with RV pressure/volume overload',
            );
          } else {
            rvParagraph.write(
              ' with interventricular septum thickness of $ivsd mm ($ivsdInterpretation)',
            );
          }
        }
      } else if (ivsd != null) {
        final String ivsdInterpretation = _interpretIVSD(ivsd);

        if (rvSize == 'dilated' && ivsd < 0.8) {
          rvParagraph.write(
            'Interventricular septum thickness is $ivsd mm ($ivsdInterpretation), consistent with RV pressure/volume overload',
          );
        } else {
          rvParagraph.write(
            'Interventricular septum thickness is $ivsd mm ($ivsdInterpretation)',
          );
        }
      }
    }

    if (tapse != null) {
      if (rvParagraph.isNotEmpty) {
        rvParagraph.write('. ');
      }

      final String rvFunction = _interpretTAPSE(tapse);
      rvParagraph.write(
        'Right ventricular systolic function is $rvFunction (TAPSE: $tapse mm)',
      );
    } else if (rvParagraph.isEmpty) {
      rvParagraph.write('Right ventricular function was not measured');
    }

    if (!rvParagraph.toString().endsWith('.')) {
      rvParagraph.write('.');
    }

    return rvParagraph.toString();
  }

  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  String _interpretTAPSE(double tapse) {
    if (tapse >= 16) return 'normal';
    if (tapse >= 13) return 'mildly reduced';
    if (tapse >= 10) return 'moderately reduced';
    return 'severely reduced';
  }

  String _interpretRVBase(double rvBase) {
    if (rvBase <= 42) return 'Normal';
    if (rvBase <= 45) return 'Mildly dilated';
    if (rvBase <= 50) return 'Moderately dilated';
    return 'Severely dilated';
  }

  String _interpretRVMid(double rvMid) {
    if (rvMid <= 35) return 'Normal';
    if (rvMid <= 40) return 'Mildly dilated';
    if (rvMid <= 45) return 'Moderately dilated';
    return 'Severely dilated';
  }

  String _interpretRVLength(double rvLength) {
    if (rvLength <= 86) return 'Normal';
    if (rvLength <= 90) return 'Mildly dilated';
    if (rvLength <= 95) return 'Moderately dilated';
    return 'Severely dilated';
  }

  String _interpretRVWallThickness(double rvWallThickness, double? ivsd) {
    if (rvWallThickness <= 5) {
      return 'Normal';
    } else {
      if (ivsd != null) {
        double ivsdMm = ivsd * 10;

        double ratio = rvWallThickness / ivsdMm;

        if (ratio > 0.75) {
          return 'Severely increased';
        } else if (ratio > 0.6) {
          return 'Moderately increased';
        } else if (ratio > 0.5) {
          return 'Mildly increased';
        } else {
          return 'Increased';
        }
      } else {
        if (rvWallThickness > 8) {
          return 'Severely increased';
        } else if (rvWallThickness > 7) {
          return 'Moderately increased';
        } else {
          return 'Mildly increased';
        }
      }
    }
  }

  String _interpretIVSD(double ivsd) {
    if (ivsd < 6) {
      return 'Thinned, suggesting RV volume overload';
    } else if (ivsd < 8) {
      return 'Mildly thinned';
    } else if (ivsd <= 11) {
      return 'Normal';
    } else if (ivsd <= 13) {
      return 'Mildly increased';
    } else if (ivsd <= 16) {
      return 'Moderately increased';
    } else {
      return 'Severely increased';
    }
  }
}
