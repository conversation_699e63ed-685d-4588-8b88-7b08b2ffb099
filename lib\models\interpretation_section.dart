class InterpretationSection {
  final String title;
  final String content;
  final List<String>? bulletPoints;
  final bool isBulletFormat;

  InterpretationSection({
    required this.title,
    required this.content,
    this.bulletPoints,
    this.isBulletFormat = false,
  });

  // Constructor for bullet point format
  InterpretationSection.withBulletPoints({
    required this.title,
    required List<String> points,
  }) : content = points.join(', '), // Fallback for backward compatibility
       bulletPoints = points,
       isBulletFormat = true;

  factory InterpretationSection.fromJson(Map<String, dynamic> json) {
    final bulletPoints = json['bulletPoints'] as List<dynamic>?;
    return InterpretationSection(
      title: json['title'],
      content: json['content'],
      bulletPoints: bulletPoints?.cast<String>(),
      isBulletFormat: json['isBulletFormat'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'content': content,
      'bulletPoints': bulletPoints,
      'isBulletFormat': isBulletFormat,
    };
  }
}
