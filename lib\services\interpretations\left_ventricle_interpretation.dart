import 'dart:math' as math;
import 'base_interpretation.dart';

class LeftVentricleInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Left ventricle assessment was not performed.';
    }

    final patientInfo = data['patientInfo'] ?? {};
    final gender = patientInfo['gender'] as String? ?? 'Not provided';
    final bsa = _parseDouble(patientInfo['bsa']);
    final echoParams = data['echoParameters'] ?? {};
    final lvedd = _parseDouble(echoParams['lvedd']);
    final lvesd = _parseDouble(echoParams['lvesd']);
    final ivsd = _parseDouble(echoParams['ivsd']);
    final lvpw = _parseDouble(echoParams['lvpw']);
    final relativeWallThickness = _parseDouble(
      echoParams['relativeWallThickness'],
    );
    final ef = _parseDouble(echoParams['ef']);
    final efMmode = _parseDouble(echoParams['efMmode']);
    final efSimpsons = _parseDouble(echoParams['efSimpsons']);
    final efEyeball = _parseDouble(echoParams['efEyeball']);
    final diastolicGrade = echoParams['diastolicGrade'] as String? ?? 'No';
    final wallMotion = data['wallMotion'] ?? {};

    final StringBuffer lvParagraph = StringBuffer();

    if (lvedd != null) {
      final bool isMale = gender.toLowerCase() == 'male';
      final String lvSize = _interpretLVSize(lvedd, isMale);

      lvParagraph.write('Is $lvSize in size');

      if (lvesd != null) {
        if (ivsd != null && lvpw != null) {
          final bool isMale = gender.toLowerCase() == 'male';
          final Map<String, dynamic> rwtResult = _processRelativeWallThickness(
            userProvidedRWT: relativeWallThickness,
            lvedd: lvedd,
            lvpw: lvpw,
            ivsd: ivsd,
            isMale: isMale,
            bsa: bsa,
          );

          if (rwtResult['geometry'].isNotEmpty) {
            final String patternName =
                rwtResult['geometry'].split('(')[0].trim();
            lvParagraph.write(' with $patternName');
          }

          if (rwtResult['rwtInterpretation'].isNotEmpty) {
            lvParagraph.write('. ${rwtResult['rwtInterpretation']}');
          }
        }
      }
    } else {
      lvParagraph.write('The left ventricle size was not measured');
    }

    if (ivsd != null && lvpw != null) {
      final bool isMale = gender.toLowerCase() == 'male';
      final String wallThickness = _interpretWallThickness(ivsd, lvpw, isMale);

      if (lvParagraph.toString().contains('with')) {
        lvParagraph.write(' and $wallThickness wall thickness');
      } else {
        lvParagraph.write(' with $wallThickness wall thickness');
      }
    }

    if (ef != null ||
        efMmode != null ||
        efSimpsons != null ||
        efEyeball != null) {
      final bool isMale = gender.toLowerCase() == 'male';
      final String efText = _formatEFWithMethods(
        efMmode,
        efSimpsons,
        efEyeball,
      );
      final double primaryEF = ef ?? efSimpsons ?? efMmode ?? efEyeball ?? 0;
      final String systolicFunction = _interpretEF(primaryEF, isMale);

      if (systolicFunction == 'Normal') {
        lvParagraph.write('. Systolic function is preserved ($efText)');
      } else if (systolicFunction == 'Mildly reduced') {
        lvParagraph.write('. Systolic function is mildly reduced ($efText)');
      } else if (systolicFunction == 'Moderately reduced') {
        lvParagraph.write(
          '. Systolic function is moderately reduced ($efText)',
        );
      } else {
        lvParagraph.write('. Systolic function is severely reduced ($efText)');
      }

      if (wallMotion.isNotEmpty) {
        final String abnormalityPresence = wallMotion['abnormality'] ?? 'no';

        if (abnormalityPresence.toLowerCase() == 'yes') {
          final String abnormalityType =
              wallMotion['abnormalityType'] ?? 'segmental';

          if (abnormalityType.toLowerCase() == 'global') {
            lvParagraph.write(' with global hypokinesia');
          } else {
            int abnormalSegments = 0;
            List<String> abnormalTypes = [];

            wallMotion.forEach((segment, status) {
              if (segment != 'abnormality' && segment != 'abnormalityType') {
                abnormalSegments++;
                if (!abnormalTypes.contains(status)) {
                  abnormalTypes.add(status);
                }
              }
            });

            if (abnormalSegments > 0) {
              lvParagraph.write(' with regional wall motion abnormality');

              if (abnormalTypes.isNotEmpty) {
                final Map<String, List<String>> segmentsByStatus = {};
                wallMotion.forEach((segment, status) {
                  if (segment != 'abnormality' &&
                      segment != 'abnormalityType') {
                    segmentsByStatus.putIfAbsent(status, () => []).add(segment);
                  }
                });

                lvParagraph.write(' in the form of');

                int statusCount = 0;
                segmentsByStatus.forEach((status, segments) {
                  if (statusCount > 0) {
                    if (statusCount == segmentsByStatus.length - 1) {
                      lvParagraph.write(' and');
                    } else {
                      lvParagraph.write(',');
                    }
                  }

                  lvParagraph.write(' $status');

                  if (segments.length == 1) {
                    lvParagraph.write(' ${segments[0]}');
                  } else {
                    for (int i = 0; i < segments.length; i++) {
                      if (i == 0) {
                        lvParagraph.write(' ${segments[i]}');
                      } else if (i == segments.length - 1) {
                        lvParagraph.write(' and ${segments[i]}');
                      } else {
                        lvParagraph.write(', ${segments[i]}');
                      }
                    }
                  }

                  statusCount++;
                });
              }
            }
          }
        }
      }
    } else {
      lvParagraph.write('. Systolic function was not quantitatively assessed');

      if (wallMotion.isNotEmpty) {
        final String abnormalityPresence = wallMotion['abnormality'] ?? 'no';

        if (abnormalityPresence.toLowerCase() == 'yes') {
          final String abnormalityType =
              wallMotion['abnormalityType'] ?? 'segmental';

          if (abnormalityType.toLowerCase() == 'global') {
            lvParagraph.write(', however global hypokinesia is present');
          } else {
            int abnormalSegments = 0;
            List<String> abnormalTypes = [];

            wallMotion.forEach((segment, status) {
              if (segment != 'abnormality' && segment != 'abnormalityType') {
                abnormalSegments++;
                if (!abnormalTypes.contains(status)) {
                  abnormalTypes.add(status);
                }
              }
            });

            if (abnormalSegments > 0) {
              lvParagraph.write(
                ', however regional wall motion abnormality is present',
              );

              if (abnormalTypes.isNotEmpty) {
                final Map<String, List<String>> segmentsByStatus = {};
                wallMotion.forEach((segment, status) {
                  if (segment != 'abnormality' &&
                      segment != 'abnormalityType') {
                    segmentsByStatus.putIfAbsent(status, () => []).add(segment);
                  }
                });

                lvParagraph.write(' in the form of');

                int statusCount = 0;
                segmentsByStatus.forEach((status, segments) {
                  if (statusCount > 0) {
                    if (statusCount == segmentsByStatus.length - 1) {
                      lvParagraph.write(' and');
                    } else {
                      lvParagraph.write(',');
                    }
                  }

                  lvParagraph.write(' $status');

                  if (segments.length == 1) {
                    lvParagraph.write(' ${segments[0]}');
                  } else {
                    for (int i = 0; i < segments.length; i++) {
                      if (i == 0) {
                        lvParagraph.write(' ${segments[i]}');
                      } else if (i == segments.length - 1) {
                        lvParagraph.write(' and ${segments[i]}');
                      } else {
                        lvParagraph.write(', ${segments[i]}');
                      }
                    }
                  }

                  statusCount++;
                });
              }
            }
          }
        }
      }
    }

    final String diastolicFunction = _interpretDiastolicFunction(
      diastolicGrade,
    );
    if (diastolicFunction == 'Normal') {
      lvParagraph.write('. Diastolic function is normal');
    } else if (diastolicFunction == 'Grade I (impaired relaxation)') {
      lvParagraph.write(
        '. Left ventricle shows grade I diastolic dysfunction (impaired relaxation pattern)',
      );
    } else if (diastolicFunction == 'Grade II (pseudonormal)') {
      lvParagraph.write(
        '. Left ventricle shows grade II diastolic dysfunction (pseudonormal filling pattern)',
      );
    } else if (diastolicFunction == 'Grade III (restrictive, reversible)') {
      lvParagraph.write(
        '. Left ventricle shows grade III diastolic dysfunction (reversible restrictive pattern)',
      );
    } else if (diastolicFunction == 'Grade IV (restrictive, irreversible)') {
      lvParagraph.write(
        '. Left ventricle shows grade IV diastolic dysfunction (irreversible restrictive pattern)',
      );
    } else {
      lvParagraph.write('. Diastolic function was not assessed');
    }

    if (!lvParagraph.toString().endsWith('.')) {
      lvParagraph.write('.');
    }

    return lvParagraph.toString();
  }

  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  String _interpretLVSize(double lvedd, bool isMale) {
    if (isMale) {
      if (lvedd < 42) return 'Small';
      if (lvedd <= 58) return 'Normal';
      if (lvedd <= 62) return 'Mildly dilated';
      if (lvedd <= 68) return 'Moderately dilated';
      return 'Severely dilated';
    } else {
      if (lvedd < 38) return 'Small';
      if (lvedd <= 52) return 'Normal';
      if (lvedd <= 56) return 'Mildly dilated';
      if (lvedd <= 61) return 'Moderately dilated';
      return 'Severely dilated';
    }
  }

  String _interpretWallThickness(double ivsd, double lvpw, bool isMale) {
    final double maxThickness = ivsd > lvpw ? ivsd : lvpw;

    if (isMale) {
      if (maxThickness <= 10) return 'Normal';
      if (maxThickness <= 13) return 'Mildly increased';
      if (maxThickness <= 16) return 'Moderately increased';
      return 'Severely increased';
    } else {
      if (maxThickness <= 9) return 'Normal';
      if (maxThickness <= 12) return 'Mildly increased';
      if (maxThickness <= 15) return 'Moderately increased';
      return 'Severely increased';
    }
  }

  String _interpretEF(double ef, bool isMale) {
    if (ef >= 55) return 'Normal';
    if (ef >= 45) return 'Mildly reduced';
    if (ef >= 30) return 'Moderately reduced';
    return 'Severely reduced';
  }

  Map<String, dynamic> _processRelativeWallThickness({
    required double? userProvidedRWT,
    required double? lvedd,
    required double lvpw,
    required double ivsd,
    required bool isMale,
    required double? bsa,
  }) {
    if (lvedd == null || lvedd <= 0) {
      return {
        'geometry': '',
        'rwtInterpretation': '',
        'rwtValue': 0.0,
        'rwtSource': 'none',
      };
    }

    final double finalRWT;
    final String rwtSource;

    if (userProvidedRWT != null) {
      finalRWT = userProvidedRWT;
      rwtSource = 'direct';
    } else {
      finalRWT = _calculateRelativeWallThickness(lvedd, lvpw);
      rwtSource = 'calculated';
    }

    final String geometry = _interpretLVGeometry(
      finalRWT,
      ivsd,
      lvpw,
      lvedd,
      isMale: isMale,
      bsa: bsa,
    );

    final String rwtInterpretation = _interpretRWTValue(
      finalRWT,
      rwtSource,
      geometry,
    );

    return {
      'geometry': geometry,
      'rwtInterpretation': rwtInterpretation,
      'rwtValue': finalRWT,
      'rwtSource': rwtSource,
    };
  }

  double _calculateRelativeWallThickness(double? lvedd, double lvpw) {
    if (lvedd == null || lvedd <= 0) return 0;
    return (2 * lvpw) / lvedd;
  }

  String _interpretRWTValue(double rwt, String source, String geometry) {
    final String sourceText = source == 'direct' ? 'measured' : 'calculated';
    final String rwtDisplay = rwt.toStringAsFixed(2);

    final bool geometryMentionsEccentric = geometry.toLowerCase().contains(
      'eccentric',
    );
    final bool geometryMentionsConcentric = geometry.toLowerCase().contains(
      'concentric',
    );
    final bool geometryMentionsHypertrophy = geometry.toLowerCase().contains(
      'hypertrophy',
    );

    if (rwt < 0.22) {
      if (geometryMentionsEccentric) {
        return 'Relative wall thickness is reduced ($sourceText: $rwtDisplay)';
      } else {
        return 'Relative wall thickness is reduced ($sourceText: $rwtDisplay), suggesting eccentric remodeling';
      }
    } else if (rwt <= 0.42) {
      return 'Relative wall thickness is normal ($sourceText: $rwtDisplay)';
    } else if (rwt <= 0.52) {
      if (geometryMentionsConcentric) {
        return 'Relative wall thickness is increased ($sourceText: $rwtDisplay)';
      } else {
        return 'Relative wall thickness is increased ($sourceText: $rwtDisplay), indicating concentric remodeling';
      }
    } else {
      if (geometryMentionsHypertrophy) {
        return 'Relative wall thickness is markedly increased ($sourceText: $rwtDisplay)';
      } else {
        return 'Relative wall thickness is markedly increased ($sourceText: $rwtDisplay), suggesting significant concentric hypertrophy';
      }
    }
  }

  String _interpretLVGeometry(
    double rwt,
    double ivsd,
    double lvpw,
    double? lvedd, {
    bool isMale = true,
    double? bsa,
  }) {
    if (lvedd == null || lvedd <= 0) return '';

    final double lveddCm = lvedd / 10;
    final double lvpwCm = lvpw / 10;
    final double ivsdCm = ivsd / 10;

    final double lvMass =
        0.8 *
            (1.04 *
                (math.pow(lveddCm + ivsdCm + lvpwCm, 3) -
                    math.pow(lveddCm, 3))) +
        0.6;

    final double actualBSA = bsa ?? (isMale ? 1.8 : 1.5);
    final double lvMassIndex = lvMass / actualBSA;

    final double lvhThreshold = isMale ? 115 : 95;
    final double severeThreshold = isMale ? 132 : 109;

    if (rwt <= 0.42) {
      if (lvMassIndex < lvhThreshold) {
        return 'normal geometry (normal wall thickness and normal LV mass)';
      } else if (lvMassIndex < severeThreshold) {
        return 'eccentric hypertrophy (normal wall thickness, increased LV cavity, increased LVM, and decreased RWT)';
      } else {
        return 'severe eccentric hypertrophy (normal wall thickness, markedly increased LV cavity, markedly increased LVM, and decreased RWT)';
      }
    } else if (rwt <= 0.52) {
      if (lvMassIndex < lvhThreshold) {
        return 'concentric remodeling (increased wall thickness, normal or small cavity, and increased RWT)';
      } else if (lvMassIndex < severeThreshold) {
        return 'concentric hypertrophy (increased wall thickness, normal cavity, increased LVM, and increased RWT)';
      } else {
        return 'severe concentric hypertrophy (markedly increased wall thickness, normal cavity, markedly increased LVM, and increased RWT)';
      }
    } else {
      if (lvMassIndex < lvhThreshold) {
        return 'asymmetric remodeling (markedly increased wall thickness and normal LV mass)';
      } else {
        return 'asymmetric hypertrophy (markedly increased wall thickness and increased LV mass)';
      }
    }
  }

  String _interpretDiastolicFunction(String grade) {
    switch (grade) {
      case 'No':
        return 'Normal';
      case 'Grade 1':
      case 'Grade I':
        return 'Grade I (impaired relaxation)';
      case 'Grade 2':
      case 'Grade II':
        return 'Grade II (pseudonormal)';
      case 'Grade 3':
      case 'Grade III':
        return 'Grade III (restrictive, reversible)';
      case 'Grade 4':
      case 'Grade IV':
        return 'Grade IV (restrictive, irreversible)';
      default:
        return 'Not assessed';
    }
  }

  String _formatEFWithMethods(
    double? efMmode,
    double? efSimpsons,
    double? efEyeball,
  ) {
    List<Map<String, dynamic>> availableEFs = [];

    if (efMmode != null) {
      availableEFs.add({'value': efMmode, 'method': 'M-Mode'});
    }
    if (efSimpsons != null) {
      availableEFs.add({'value': efSimpsons, 'method': 'Simpson\'s'});
    }
    if (efEyeball != null) {
      availableEFs.add({'value': efEyeball, 'method': 'Eyeball'});
    }

    if (availableEFs.isEmpty) {
      return 'EF: Not measured';
    }

    if (availableEFs.length == 1) {
      final ef = availableEFs.first;
      return 'EF: ${ef['value']}% by ${ef['method']}';
    }

    // Check if all values are the same
    final firstValue = availableEFs.first['value'];
    final bool allSame = availableEFs.every((ef) => ef['value'] == firstValue);

    if (allSame) {
      // All values are the same, group methods
      final methods = availableEFs.map((ef) => ef['method']).join(' and ');
      return 'EF: $firstValue% by $methods';
    } else {
      // Values are different, show each separately
      final efStrings =
          availableEFs
              .map((ef) => '${ef['value']}% by ${ef['method']}')
              .toList();
      if (efStrings.length == 2) {
        return 'EF: ${efStrings.join(' and ')}';
      } else {
        final lastMethod = efStrings.removeLast();
        return 'EF: ${efStrings.join(', ')} and $lastMethod';
      }
    }
  }
}
