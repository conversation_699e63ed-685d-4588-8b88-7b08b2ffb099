import 'package:flutter/material.dart';

import 'package:flutter/services.dart';

import '../models/echo_enums.dart';
import 'quick_pdf_preview_screen.dart';
import '../utils/bsa_calculator.dart';
import '../services/modular_interpretation_service.dart';
import '../models/interpretation_section.dart';
import '../services/smart_template_service.dart';
import '../models/smart_template_model.dart';
import '../constants/font_sizes.dart';

class QuickReportScreen extends StatefulWidget {
  final SmartTemplate? template;

  const QuickReportScreen({super.key, this.template});

  @override
  State<QuickReportScreen> createState() => _QuickReportScreenState();
}

class _QuickReportScreenState extends State<QuickReportScreen> {
  final _formKey = GlobalKey<FormState>();

  static final Color _primaryColor = Colors.orange.shade800;
  static final Color _primaryColorLight = Colors.orange.shade50;
  static final Color _borderColor = Colors.grey.shade300;
  static final Color _shadowColor = Colors.grey.shade400;
  static final Color _helperTextColor = Colors.grey.shade600;
  static final Color _helperTextColorDark = Colors.grey.shade700;
  static const Color _textColor = Colors.black87;
  static const Color _fillColor = Colors.white;
  static const Color _backgroundColor = Color(0xFFFAFAFA);

  static final Color _successColor = Colors.green.shade700;
  static final Color _warningColor = Colors.orange.shade700;
  static final Color _errorColor = Colors.red.shade700;
  static final Color _infoColor = Colors.blue.shade700;
  static final Color _purpleColor = Colors.purple.shade700;
  static final Color _amberColor = Colors.amber.shade700;

  static const Map<WallMotionScore, Color> _wallMotionColors = {
    WallMotionScore.normal: Color.fromRGBO(0, 204, 0, 1),
    WallMotionScore.hypokinetic: Color.fromRGBO(255, 204, 0, 1),
    WallMotionScore.akinetic: Color.fromRGBO(255, 102, 0, 1),
    WallMotionScore.dyskinetic: Color.fromRGBO(165, 42, 42, 1),
    WallMotionScore.aneurysmal: Color.fromRGBO(204, 0, 255, 1),
  };

  Widget _buildSectionHeader(String title, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
      decoration: BoxDecoration(
        color: _primaryColorLight,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: _shadowColor.withAlpha(50),
            blurRadius: 2,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: _primaryColor, size: 24),
          const SizedBox(width: 10),
          Text(
            title,
            style: const TextStyle(
              fontSize: FontSizes.heading1,
              fontWeight: FontWeight.bold,
              color: _textColor,
            ),
          ),
        ],
      ),
    );
  }

  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();

  final _patientNameController = TextEditingController();
  final _ageController = TextEditingController();
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();

  final _lveddController = TextEditingController();
  final _lvesdController = TextEditingController();
  final _ivsdController = TextEditingController();
  final _lvpwController = TextEditingController();
  final _relativeWallThicknessController = TextEditingController();
  final _laController = TextEditingController();
  final _aoController = TextEditingController();
  final _efController = TextEditingController();
  final _fsController = TextEditingController();
  final _tapseController = TextEditingController();
  final _paspController = TextEditingController();

  String _selectedDiastolicGrade = 'No';

  WallMotionAbnormality _wallMotionAbnormality = WallMotionAbnormality.no;
  WallMotionAbnormalityType _wallMotionAbnormalityType =
      WallMotionAbnormalityType.segmental;

  final Map<LVSegment, WallMotionScore> _wallMotionScores = {
    LVSegment.basalAnterior: WallMotionScore.normal,
    LVSegment.basalAnteroseptal: WallMotionScore.normal,
    LVSegment.basalInferoseptal: WallMotionScore.normal,
    LVSegment.basalInferior: WallMotionScore.normal,
    LVSegment.basalInferolateral: WallMotionScore.normal,
    LVSegment.basalAnterolateral: WallMotionScore.normal,
    LVSegment.midAnterior: WallMotionScore.normal,
    LVSegment.midAnteroseptal: WallMotionScore.normal,
    LVSegment.midInferoseptal: WallMotionScore.normal,
    LVSegment.midInferior: WallMotionScore.normal,
    LVSegment.midInferolateral: WallMotionScore.normal,
    LVSegment.midAnterolateral: WallMotionScore.normal,
    LVSegment.apicalAnterior: WallMotionScore.normal,
    LVSegment.apicalSeptal: WallMotionScore.normal,
    LVSegment.apicalInferior: WallMotionScore.normal,
    LVSegment.apicalLateral: WallMotionScore.normal,
    LVSegment.apex: WallMotionScore.normal,
  };

  final Map<String, ValveStructuralAbnormality> _valveMorphology = {
    'Mitral': ValveStructuralAbnormality.normal,
    'Aortic': ValveStructuralAbnormality.normal,
    'Tricuspid': ValveStructuralAbnormality.normal,
    'Pulmonary': ValveStructuralAbnormality.normal,
  };

  CardiacRhythm _mitralValveRhythm = CardiacRhythm.sinusRhythm;

  final Map<String, TextEditingController> _valveMorphologyOtherControllers = {
    'Mitral': TextEditingController(),
    'Aortic': TextEditingController(),
    'Tricuspid': TextEditingController(),
    'Pulmonary': TextEditingController(),
  };

  final Map<String, ProstheticValveType> _prostheticValveTypes = {
    'Mitral': ProstheticValveType.mechanical,
    'Aortic': ProstheticValveType.mechanical,
    'Tricuspid': ProstheticValveType.mechanical,
    'Pulmonary': ProstheticValveType.mechanical,
  };

  final Map<String, ProstheticValveFunction> _prostheticValveFunction = {
    'Mitral': ProstheticValveFunction.normal,
    'Aortic': ProstheticValveFunction.normal,
    'Tricuspid': ProstheticValveFunction.normal,
    'Pulmonary': ProstheticValveFunction.normal,
  };

  final Map<String, StenosisSeverity> _valveStenosis = {
    'Mitral': StenosisSeverity.none,
    'Aortic': StenosisSeverity.none,
    'Tricuspid': StenosisSeverity.none,
    'Pulmonary': StenosisSeverity.none,
  };

  final Map<String, RegurgitationSeverity> _valveRegurgitation = {
    'Mitral': RegurgitationSeverity.none,
    'Aortic': RegurgitationSeverity.none,
    'Tricuspid': RegurgitationSeverity.none,
    'Pulmonary': RegurgitationSeverity.none,
  };

  PericardialEffusionSeverity _pericardialEffusionSeverity =
      PericardialEffusionSeverity.none;
  PericardialEffusionLocation _pericardialEffusionLocation =
      PericardialEffusionLocation.none;

  IVCCollapsibility _ivcCollapsibility = IVCCollapsibility.normal;
  final TextEditingController _ivcDiameterController = TextEditingController();

  RVSize _rvSize = RVSize.normal;
  final TextEditingController _rvBaseController = TextEditingController();
  final TextEditingController _rvMidController = TextEditingController();
  final TextEditingController _rvLengthController = TextEditingController();
  final TextEditingController _rvWallThicknessController =
      TextEditingController();

  RASize _raSize = RASize.normal;

  SeptalDeformity _septalDeformity = SeptalDeformity.none;
  UnusualFinding _unusualFinding = UnusualFinding.none;
  final TextEditingController _unusualFindingLocationController =
      TextEditingController();
  final TextEditingController _unusualFindingDescriptionController =
      TextEditingController();

  Gender? _selectedGender;

  Priority _selectedPriority = Priority.routine;
  final _indicationController = TextEditingController();
  final _requestedByController = TextEditingController();
  bool _showRequestedByField = false;
  Location _selectedLocation = Location.outpatient;

  @override
  void initState() {
    super.initState();

    if (widget.template != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadTemplateData(widget.template!);
      });
    }
  }

  @override
  void dispose() {
    _patientNameController.dispose();
    _ageController.dispose();
    _heightController.dispose();
    _weightController.dispose();

    _lveddController.dispose();
    _lvesdController.dispose();
    _ivsdController.dispose();
    _lvpwController.dispose();
    _relativeWallThicknessController.dispose();
    _laController.dispose();
    _aoController.dispose();
    _efController.dispose();
    _fsController.dispose();
    _tapseController.dispose();
    _paspController.dispose();
    _ivcDiameterController.dispose();
    _rvBaseController.dispose();
    _rvMidController.dispose();
    _rvLengthController.dispose();
    _rvWallThicknessController.dispose();
    _unusualFindingLocationController.dispose();
    _unusualFindingDescriptionController.dispose();
    _indicationController.dispose();
    _requestedByController.dispose();

    _valveMorphologyOtherControllers.forEach((_, controller) {
      controller.dispose();
    });

    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: _primaryColor,
              onPrimary: _fillColor,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(foregroundColor: _primaryColor),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: _primaryColor,
              onPrimary: _fillColor,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(foregroundColor: _primaryColor),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  InputDecoration _createPatientInfoInputDecoration(
    String label,
    IconData icon,
  ) {
    return InputDecoration(
      labelText: label,
      labelStyle: TextStyle(
        color: _primaryColor,
        fontSize: FontSizes.labelLarge,
      ),
      prefixIcon: Icon(icon, color: _primaryColor),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: _borderColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: _borderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: _primaryColor, width: 2),
      ),
      filled: true,
      fillColor: _fillColor,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
      floatingLabelStyle: TextStyle(
        color: _primaryColor,
        fontSize: FontSizes.labelLarge,
      ),
    );
  }

  InputDecoration _createEchoInputDecoration(String label, String normalValue) {
    return InputDecoration(
      labelText: label,
      labelStyle: TextStyle(
        color: _primaryColor,
        fontSize: FontSizes.labelLarge,
      ),
      helperText: 'Normal: $normalValue',
      helperStyle: TextStyle(
        color: _helperTextColorDark,
        fontSize: FontSizes.helperText,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: _borderColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: _borderColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(color: _primaryColor, width: 2),
      ),
      filled: true,
      fillColor: _fillColor,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      floatingLabelStyle: TextStyle(
        color: _primaryColor,
        fontSize: FontSizes.labelLarge,
      ),
    );
  }

  // ignore: unused_element
  Widget _createWallMotionAbnormalityDropdown() {
    return DropdownButtonFormField<WallMotionAbnormality>(
      decoration: InputDecoration(
        labelText: 'Wall Motion Abnormality',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        isDense: true,
      ),
      isExpanded: true,
      value: _wallMotionAbnormality,
      items:
          WallMotionAbnormality.values.map((abnormality) {
            return DropdownMenuItem<WallMotionAbnormality>(
              value: abnormality,
              child: Text(
                abnormality == WallMotionAbnormality.yes ? 'Yes' : 'No',
                style: TextStyle(
                  color:
                      abnormality == WallMotionAbnormality.yes
                          ? _errorColor
                          : _successColor,
                  fontSize: FontSizes.dropdownItem,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          }).toList(),
      onChanged: (WallMotionAbnormality? value) {
        if (value != null) {
          setState(() {
            _wallMotionAbnormality = value;

            if (value == WallMotionAbnormality.no) {
              _wallMotionScores.forEach((segment, _) {
                _wallMotionScores[segment] = WallMotionScore.normal;
              });
            }
          });
        }
      },
    );
  }

  // ignore: unused_element
  Widget _createWallMotionAbnormalityTypeDropdown() {
    return DropdownButtonFormField<WallMotionAbnormalityType>(
      decoration: InputDecoration(
        labelText: 'Abnormality Type',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        isDense: true,
      ),
      isExpanded: true,
      value: _wallMotionAbnormalityType,
      items:
          WallMotionAbnormalityType.values.map((type) {
            return DropdownMenuItem<WallMotionAbnormalityType>(
              value: type,
              child: Text(
                type == WallMotionAbnormalityType.global
                    ? 'Global'
                    : 'Segmental',
                style: const TextStyle(
                  color: _textColor,
                  fontSize: FontSizes.dropdownItem,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          }).toList(),
      onChanged: (WallMotionAbnormalityType? value) {
        if (value != null) {
          setState(() {
            _wallMotionAbnormalityType = value;

            if (value == WallMotionAbnormalityType.global) {
              _wallMotionScores.forEach((segment, _) {
                _wallMotionScores[segment] = WallMotionScore.hypokinetic;
              });
            }
          });
        }
      },
    );
  }

  Widget _createWallMotionDropdown(LVSegment segment) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return DropdownButtonFormField<WallMotionScore>(
          decoration: InputDecoration(
            labelText: segment.displayName,
            labelStyle: TextStyle(
              color: _primaryColor,
              fontSize: FontSizes.labelLarge,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: _borderColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: _borderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: _primaryColor, width: 2),
            ),
            filled: true,
            fillColor: _fillColor,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 16,
            ),
            isDense: true,
          ),
          isExpanded: true,
          value: _wallMotionScores[segment],
          items:
              WallMotionScore.values.map((score) {
                return DropdownMenuItem<WallMotionScore>(
                  value: score,
                  child: Text(
                    score.toString().split('.').last,
                    style: TextStyle(
                      color: _wallMotionColors[score]!,
                      fontSize: FontSizes.dropdownItem,
                      fontWeight:
                          _wallMotionScores[segment] == score
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                );
              }).toList(),
          onChanged: (WallMotionScore? value) {
            if (value != null) {
              setState(() {
                _wallMotionScores[segment] = value;
              });
            }
          },
        );
      },
    );
  }

  Widget _createValveMorphologyDropdown(String valve) {
    String getDisplayText(ValveStructuralAbnormality morphology) {
      switch (morphology) {
        case ValveStructuralAbnormality.normal:
          return 'Normal';
        case ValveStructuralAbnormality.bicuspid:
          return 'Bicuspid';
        case ValveStructuralAbnormality.calcified:
          return 'Calcified';
        case ValveStructuralAbnormality.myxomatous:
          return 'Myxomatous';
        case ValveStructuralAbnormality.rheumatic:
          return 'Rheumatic';
        case ValveStructuralAbnormality.thrombus:
          return 'Thrombus';
        case ValveStructuralAbnormality.vegetation:
          return 'Vegetation';
        case ValveStructuralAbnormality.prosthetic:
          return 'Prosthetic';
        case ValveStructuralAbnormality.other:
          return 'Other';
      }
    }

    return DropdownButtonFormField<ValveStructuralAbnormality>(
      decoration: InputDecoration(
        labelText: 'Morphology',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      style: const TextStyle(
        color: _textColor,
        fontSize: FontSizes.dropdownItem,
      ),
      value: _valveMorphology[valve],
      items:
          ValveStructuralAbnormality.values.map((morphology) {
            return DropdownMenuItem<ValveStructuralAbnormality>(
              value: morphology,
              child: Text(
                getDisplayText(morphology),
                style: TextStyle(
                  fontSize: FontSizes.dropdownItem,
                  fontWeight:
                      _valveMorphology[valve] == morphology
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
      onChanged: (ValveStructuralAbnormality? value) {
        if (value != null) {
          setState(() {
            _valveMorphology[valve] = value;
          });
        }
      },
    );
  }

  Widget _createProstheticValveTypeDropdown(String valve) {
    String getDisplayText(ProstheticValveType type) {
      switch (type) {
        case ProstheticValveType.mechanical:
          return 'Mechanical';
        case ProstheticValveType.bioprosthetic:
          return 'Bioprosthetic';
      }
    }

    return DropdownButtonFormField<ProstheticValveType>(
      decoration: InputDecoration(
        labelText: 'Prosthetic Type',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      style: const TextStyle(
        color: _textColor,
        fontSize: FontSizes.dropdownItem,
      ),
      value: _prostheticValveTypes[valve],
      items:
          ProstheticValveType.values.map((type) {
            return DropdownMenuItem<ProstheticValveType>(
              value: type,
              child: Text(
                getDisplayText(type),
                style: TextStyle(
                  fontSize: FontSizes.dropdownItem,
                  fontWeight:
                      _prostheticValveTypes[valve] == type
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
      onChanged: (ProstheticValveType? value) {
        if (value != null) {
          setState(() {
            _prostheticValveTypes[valve] = value;
          });
        }
      },
    );
  }

  Widget _createProstheticValveFunctionDropdown(String valve) {
    String getDisplayText(ProstheticValveFunction function) {
      switch (function) {
        case ProstheticValveFunction.normal:
          return 'Normal';

        case ProstheticValveFunction.svdLeafletCalcification:
          return 'SVD - Leaflet Calcification';
        case ProstheticValveFunction.svdLeafletTear:
          return 'SVD - Leaflet Tear';
        case ProstheticValveFunction.svdStentFracture:
          return 'SVD - Stent Fracture';

        case ProstheticValveFunction.ppm:
          return 'Prosthesis-Patient Mismatch';
        case ProstheticValveFunction.paravalvularLeak:
          return 'Paravalvular Leak';
        case ProstheticValveFunction.pannus:
          return 'Pannus';
        case ProstheticValveFunction.malposition:
          return 'Malposition';

        case ProstheticValveFunction.endocarditis:
          return 'Endocarditis';

        case ProstheticValveFunction.thrombus:
          return 'Thrombus';
      }
    }

    Color getTextColor(ProstheticValveFunction function) {
      switch (function) {
        case ProstheticValveFunction.normal:
          return _successColor;

        case ProstheticValveFunction.svdLeafletCalcification:
        case ProstheticValveFunction.svdLeafletTear:
        case ProstheticValveFunction.svdStentFracture:
          return _warningColor;

        case ProstheticValveFunction.ppm:
        case ProstheticValveFunction.paravalvularLeak:
        case ProstheticValveFunction.pannus:
        case ProstheticValveFunction.malposition:
          return _warningColor;

        case ProstheticValveFunction.endocarditis:
        case ProstheticValveFunction.thrombus:
          return _errorColor;
      }
    }

    return DropdownButtonFormField<ProstheticValveFunction>(
      decoration: InputDecoration(
        labelText: 'Prosthetic Function',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      style: const TextStyle(
        color: _textColor,
        fontSize: FontSizes.dropdownItem,
      ),
      value: _prostheticValveFunction[valve],
      items:
          ProstheticValveFunction.values.map((function) {
            return DropdownMenuItem<ProstheticValveFunction>(
              value: function,
              child: Text(
                getDisplayText(function),
                style: TextStyle(
                  color: getTextColor(function),
                  fontSize: FontSizes.dropdownItem,
                  fontWeight:
                      _prostheticValveFunction[valve] == function
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
      onChanged: (ProstheticValveFunction? value) {
        if (value != null) {
          setState(() {
            _prostheticValveFunction[valve] = value;
          });
        }
      },
    );
  }

  Widget _createValveStenosisDropdown(String valve) {
    String getDisplayText(StenosisSeverity severity) {
      switch (severity) {
        case StenosisSeverity.none:
          return 'None';
        case StenosisSeverity.mild:
          return 'Mild';
        case StenosisSeverity.moderate:
          return 'Moderate';
        case StenosisSeverity.severe:
          return 'Severe';
        case StenosisSeverity.critical:
          return 'Critical';
      }
    }

    return DropdownButtonFormField<StenosisSeverity>(
      decoration: InputDecoration(
        labelText: 'Stenosis',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      style: const TextStyle(
        color: _textColor,
        fontSize: FontSizes.dropdownItem,
      ),
      value: _valveStenosis[valve],
      items:
          StenosisSeverity.values.map((severity) {
            Color textColor;
            switch (severity) {
              case StenosisSeverity.none:
                textColor = _successColor;
                break;
              case StenosisSeverity.mild:
                textColor = _amberColor;
                break;
              case StenosisSeverity.moderate:
                textColor = _warningColor;
                break;
              case StenosisSeverity.severe:
              case StenosisSeverity.critical:
                textColor = _errorColor;
                break;
            }

            return DropdownMenuItem<StenosisSeverity>(
              value: severity,
              child: Text(
                getDisplayText(severity),
                style: TextStyle(
                  color: textColor,
                  fontSize: FontSizes.dropdownItem,
                  fontWeight:
                      _valveStenosis[valve] == severity
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
      onChanged: (StenosisSeverity? value) {
        if (value != null) {
          setState(() {
            _valveStenosis[valve] = value;
          });
        }
      },
    );
  }

  Widget _createValveRegurgitationDropdown(String valve) {
    String getDisplayText(RegurgitationSeverity severity) {
      switch (severity) {
        case RegurgitationSeverity.none:
          return 'None';
        case RegurgitationSeverity.trace:
          return 'Trace';
        case RegurgitationSeverity.mild:
          return 'Mild';
        case RegurgitationSeverity.moderate:
          return 'Moderate';
        case RegurgitationSeverity.moderateSevere:
          return 'Mod-Severe';
        case RegurgitationSeverity.severe:
          return 'Severe';
      }
    }

    return DropdownButtonFormField<RegurgitationSeverity>(
      decoration: InputDecoration(
        labelText: 'Regurgitation',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      style: const TextStyle(
        color: _textColor,
        fontSize: FontSizes.dropdownItem,
      ),
      value: _valveRegurgitation[valve],
      items:
          RegurgitationSeverity.values.map((severity) {
            Color textColor;
            switch (severity) {
              case RegurgitationSeverity.none:
                textColor = _successColor;
                break;
              case RegurgitationSeverity.trace:
              case RegurgitationSeverity.mild:
                textColor = _amberColor;
                break;
              case RegurgitationSeverity.moderate:
              case RegurgitationSeverity.moderateSevere:
                textColor = _warningColor;
                break;
              case RegurgitationSeverity.severe:
                textColor = _errorColor;
                break;
            }

            return DropdownMenuItem<RegurgitationSeverity>(
              value: severity,
              child: Text(
                getDisplayText(severity),
                style: TextStyle(
                  color: textColor,
                  fontSize: FontSizes.dropdownItem,
                  fontWeight:
                      _valveRegurgitation[valve] == severity
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
      onChanged: (RegurgitationSeverity? value) {
        if (value != null) {
          setState(() {
            _valveRegurgitation[valve] = value;
          });
        }
      },
    );
  }

  Widget _createPericardialEffusionSeverityDropdown() {
    String getDisplayText(PericardialEffusionSeverity severity) {
      switch (severity) {
        case PericardialEffusionSeverity.none:
          return 'None';
        case PericardialEffusionSeverity.small:
          return 'Small';
        case PericardialEffusionSeverity.moderate:
          return 'Moderate';
        case PericardialEffusionSeverity.severe:
          return 'Severe';
        case PericardialEffusionSeverity.tamponade:
          return 'Tamponade';
      }
    }

    return DropdownButtonFormField<PericardialEffusionSeverity>(
      decoration: InputDecoration(
        labelText: 'Severity',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      style: const TextStyle(
        color: _textColor,
        fontSize: FontSizes.dropdownItem,
      ),
      value: _pericardialEffusionSeverity,
      items:
          PericardialEffusionSeverity.values.map((severity) {
            Color textColor;
            switch (severity) {
              case PericardialEffusionSeverity.none:
                textColor = _successColor;
                break;
              case PericardialEffusionSeverity.small:
                textColor = _amberColor;
                break;
              case PericardialEffusionSeverity.moderate:
                textColor = _warningColor;
                break;
              case PericardialEffusionSeverity.severe:
              case PericardialEffusionSeverity.tamponade:
                textColor = _errorColor;
                break;
            }

            return DropdownMenuItem<PericardialEffusionSeverity>(
              value: severity,
              child: Text(
                getDisplayText(severity),
                style: TextStyle(
                  color: textColor,
                  fontSize: FontSizes.dropdownItem,
                  fontWeight:
                      _pericardialEffusionSeverity == severity
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
      onChanged: (PericardialEffusionSeverity? value) {
        if (value != null) {
          setState(() {
            _pericardialEffusionSeverity = value;
          });
        }
      },
    );
  }

  Widget _createCardiacRhythmDropdown() {
    String getDisplayText(CardiacRhythm rhythm) {
      switch (rhythm) {
        case CardiacRhythm.sinusRhythm:
          return 'Sinus Rhythm';
        case CardiacRhythm.atrialFibrillation:
          return 'Atrial Fibrillation';
        case CardiacRhythm.atrialFlutter:
          return 'Atrial Flutter';
        case CardiacRhythm.ectopicAtrialRhythm:
          return 'Ectopic Atrial Rhythm';
        case CardiacRhythm.junctionalRhythm:
          return 'Junctional Rhythm';
        case CardiacRhythm.ventricularRhythm:
          return 'Ventricular Rhythm';
        case CardiacRhythm.heartBlock:
          return 'Heart Block';
        case CardiacRhythm.pacedRhythm:
          return 'Paced Rhythm';
        case CardiacRhythm.other:
          return 'Other';
      }
    }

    return DropdownButtonFormField<CardiacRhythm>(
      decoration: InputDecoration(
        labelText: 'Rhythm',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      value: _mitralValveRhythm,
      items:
          CardiacRhythm.values
              .where((rhythm) => rhythm != CardiacRhythm.other)
              .map((rhythm) {
                return DropdownMenuItem<CardiacRhythm>(
                  value: rhythm,
                  child: Text(
                    getDisplayText(rhythm),
                    style: TextStyle(
                      fontSize: FontSizes.dropdownItem,
                      fontWeight:
                          _mitralValveRhythm == rhythm
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                );
              })
              .toList(),
      onChanged: (CardiacRhythm? value) {
        if (value != null) {
          setState(() {
            _mitralValveRhythm = value;
          });
        }
      },
    );
  }

  Widget _createPericardialEffusionLocationDropdown() {
    String getDisplayText(PericardialEffusionLocation location) {
      switch (location) {
        case PericardialEffusionLocation.none:
          return 'None';
        case PericardialEffusionLocation.circumferential:
          return 'Circumferential';
        case PericardialEffusionLocation.anterior:
          return 'Anterior';
        case PericardialEffusionLocation.posterior:
          return 'Posterior';
        case PericardialEffusionLocation.lateral:
          return 'Lateral';
        case PericardialEffusionLocation.apical:
          return 'Apical';
        case PericardialEffusionLocation.loculated:
          return 'Loculated';
      }
    }

    return DropdownButtonFormField<PericardialEffusionLocation>(
      decoration: InputDecoration(
        labelText: 'Location',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      style: const TextStyle(
        color: _textColor,
        fontSize: FontSizes.dropdownItem,
      ),
      value: _pericardialEffusionLocation,
      items:
          PericardialEffusionLocation.values.map((location) {
            return DropdownMenuItem<PericardialEffusionLocation>(
              value: location,
              child: Text(
                getDisplayText(location),
                style: TextStyle(
                  fontSize: FontSizes.dropdownItem,
                  fontWeight:
                      _pericardialEffusionLocation == location
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
      onChanged: (PericardialEffusionLocation? value) {
        if (value != null) {
          setState(() {
            _pericardialEffusionLocation = value;
          });
        }
      },
    );
  }

  Widget _createIVCCollapsibilityDropdown() {
    String getDisplayText(IVCCollapsibility collapsibility) {
      switch (collapsibility) {
        case IVCCollapsibility.normal:
          return '≥50% (Normal)';
        case IVCCollapsibility.reduced:
          return '<50% (Reduced)';
        case IVCCollapsibility.absent:
          return 'Absent';
      }
    }

    return DropdownButtonFormField<IVCCollapsibility>(
      decoration: InputDecoration(
        labelText: 'Collapsibility',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      style: const TextStyle(
        color: _textColor,
        fontSize: FontSizes.dropdownItem,
      ),
      value: _ivcCollapsibility,
      items:
          IVCCollapsibility.values.map((collapsibility) {
            Color textColor;
            switch (collapsibility) {
              case IVCCollapsibility.normal:
                textColor = _successColor;
                break;
              case IVCCollapsibility.reduced:
                textColor = _warningColor;
                break;
              case IVCCollapsibility.absent:
                textColor = _errorColor;
                break;
            }

            return DropdownMenuItem<IVCCollapsibility>(
              value: collapsibility,
              child: Text(
                getDisplayText(collapsibility),
                style: TextStyle(
                  color: textColor,
                  fontSize: FontSizes.dropdownItem,
                  fontWeight:
                      _ivcCollapsibility == collapsibility
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
      onChanged: (IVCCollapsibility? value) {
        if (value != null) {
          setState(() {
            _ivcCollapsibility = value;
          });
        }
      },
    );
  }

  Widget _createSeptalDeformityDropdown() {
    String getDisplayText(SeptalDeformity deformity) {
      switch (deformity) {
        case SeptalDeformity.none:
          return 'None';
        case SeptalDeformity.hypertrophy:
          return 'Hypertrophy';
        case SeptalDeformity.flattening:
          return 'Flattening';
        case SeptalDeformity.bowing:
          return 'Bowing';
        case SeptalDeformity.aneurysm:
          return 'Aneurysm';
        case SeptalDeformity.vsd:
          return 'VSD';
        case SeptalDeformity.rightToLeftShunt:
          return 'Right-to-Left Shunt';
      }
    }

    return DropdownButtonFormField<SeptalDeformity>(
      decoration: InputDecoration(
        labelText: 'Septal Deformity',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      style: const TextStyle(
        color: _textColor,
        fontSize: FontSizes.dropdownItem,
      ),
      value: _septalDeformity,
      items:
          SeptalDeformity.values.map((deformity) {
            Color textColor;
            switch (deformity) {
              case SeptalDeformity.none:
                textColor = _successColor;
                break;
              case SeptalDeformity.hypertrophy:
              case SeptalDeformity.flattening:
              case SeptalDeformity.bowing:
                textColor = _warningColor;
                break;
              case SeptalDeformity.aneurysm:
              case SeptalDeformity.vsd:
              case SeptalDeformity.rightToLeftShunt:
                textColor = _errorColor;
                break;
            }

            return DropdownMenuItem<SeptalDeformity>(
              value: deformity,
              child: Text(
                getDisplayText(deformity),
                style: TextStyle(
                  color: textColor,
                  fontSize: FontSizes.dropdownItem,
                  fontWeight:
                      _septalDeformity == deformity
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
      onChanged: (SeptalDeformity? value) {
        if (value != null) {
          setState(() {
            _septalDeformity = value;
          });
        }
      },
    );
  }

  Widget _createUnusualFindingDropdown() {
    String getDisplayText(UnusualFinding finding) {
      switch (finding) {
        case UnusualFinding.none:
          return 'None';
        case UnusualFinding.mass:
          return 'Mass';
        case UnusualFinding.thrombus:
          return 'Thrombus';
        case UnusualFinding.vegetation:
          return 'Vegetation';
        case UnusualFinding.rupture:
          return 'Rupture';
        case UnusualFinding.pseudoaneurysm:
          return 'Pseudoaneurysm';
        case UnusualFinding.dissection:
          return 'Aortic Dissection';
        case UnusualFinding.flap:
          return 'Intimal Flap';
        case UnusualFinding.intimalTear:
          return 'Intimal Tear';
        case UnusualFinding.apicalBallooning:
          return 'Apical Ballooning';
        case UnusualFinding.takotsubo:
          return 'Takotsubo Cardiomyopathy';
        case UnusualFinding.cardiacTamponade:
          return 'Cardiac Tamponade';
        case UnusualFinding.restrictivePhysiology:
          return 'Restrictive Physiology';
        case UnusualFinding.constrictivePericarditis:
          return 'Constrictive Pericarditis';
        case UnusualFinding.hypertrophicObstruction:
          return 'Hypertrophic Obstruction';
        case UnusualFinding.cardiacAmyloidosis:
          return 'Cardiac Amyloidosis';
        case UnusualFinding.cardiacSarcoidosis:
          return 'Cardiac Sarcoidosis';
        case UnusualFinding.cardiacTumor:
          return 'Cardiac Tumor';
        case UnusualFinding.cardiacMetastasis:
          return 'Cardiac Metastasis';
        case UnusualFinding.pulmonaryEmbolism:
          return 'Pulmonary Embolism';
        case UnusualFinding.infectiveEndocarditis:
          return 'Infective Endocarditis';
        case UnusualFinding.other:
          return 'Other';
      }
    }

    return DropdownButtonFormField<UnusualFinding>(
      decoration: InputDecoration(
        labelText: 'Unusual Finding',
        labelStyle: TextStyle(
          color: _primaryColor,
          fontSize: FontSizes.labelLarge,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: _primaryColor, width: 2),
        ),
        filled: true,
        fillColor: _fillColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        isDense: true,
      ),
      isExpanded: true,
      style: const TextStyle(
        color: _textColor,
        fontSize: FontSizes.dropdownItem,
      ),
      value: _unusualFinding,
      items:
          UnusualFinding.values.map((finding) {
            Color textColor;

            if (finding == UnusualFinding.none) {
              textColor = _successColor;
            } else if ([
              UnusualFinding.vegetation,
              UnusualFinding.rupture,
              UnusualFinding.pseudoaneurysm,
              UnusualFinding.dissection,
              UnusualFinding.flap,
              UnusualFinding.intimalTear,
              UnusualFinding.cardiacTamponade,
              UnusualFinding.cardiacMetastasis,
              UnusualFinding.pulmonaryEmbolism,
              UnusualFinding.infectiveEndocarditis,
            ].contains(finding)) {
              textColor = _errorColor;
            } else if ([
              UnusualFinding.mass,
              UnusualFinding.thrombus,
              UnusualFinding.apicalBallooning,
              UnusualFinding.takotsubo,
              UnusualFinding.restrictivePhysiology,
              UnusualFinding.constrictivePericarditis,
              UnusualFinding.hypertrophicObstruction,
              UnusualFinding.cardiacAmyloidosis,
              UnusualFinding.cardiacSarcoidosis,
              UnusualFinding.cardiacTumor,
              UnusualFinding.other,
            ].contains(finding)) {
              textColor = _warningColor;
            } else {
              textColor = _infoColor;
            }

            return DropdownMenuItem<UnusualFinding>(
              value: finding,
              child: Text(
                getDisplayText(finding),
                style: TextStyle(
                  color: textColor,
                  fontSize: FontSizes.dropdownItem,
                  fontWeight:
                      _unusualFinding == finding
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
      onChanged: (UnusualFinding? value) {
        if (value != null) {
          setState(() {
            _unusualFinding = value;
          });
        }
      },
    );
  }

  Future<void> _showSaveTemplateDialog() async {
    final result = await showDialog<Map<String, String>?>(
      context: context,
      barrierDismissible: false,
      builder: (context) => const _SaveTemplateDialog(),
    );

    if (result != null && result['name']?.trim().isNotEmpty == true) {
      await _saveCurrentFormAsTemplate(
        result['name']!.trim(),
        result['description']?.trim() ?? '',
      );
    }
  }

  Future<void> _saveCurrentFormAsTemplate(
    String name,
    String description,
  ) async {
    try {
      final templateData = _collectCurrentFormData();

      await SmartTemplateService.createTemplateFromQuickReportData(
        name,
        description.isEmpty ? 'Custom template' : description,
        templateData,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Template "$name" saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving template: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showLoadTemplateDialog() async {
    try {
      final templates = await SmartTemplateService.getTemplates();

      if (templates.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No templates available. Create one first!'),
            ),
          );
        }
        return;
      }

      if (mounted) {
        final selectedTemplate = await showDialog<SmartTemplate>(
          context: context,
          barrierDismissible: true,
          builder:
              (context) => Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                elevation: 16,
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.9,
                  height: MediaQuery.of(context).size.height * 0.7,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.orange.shade50,
                        Colors.white,
                        Colors.orange.shade50,
                      ],
                    ),
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade600,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20),
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.download,
                                color: Colors.orange.shade600,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Load Template',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: FontSizes.heading2,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Choose a template to load into the form',
                                    style: TextStyle(
                                      color: Colors.orange.shade100,
                                      fontSize: FontSizes.bodySmall,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              onPressed: () => Navigator.of(context).pop(),
                              icon: const Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),

                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: ListView.builder(
                            itemCount: templates.length,
                            itemBuilder: (context, index) {
                              final template = templates[index];
                              return Container(
                                margin: const EdgeInsets.only(bottom: 12),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withAlpha(15),
                                      blurRadius: 3,
                                      spreadRadius: 1,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(16),
                                    onTap:
                                        () =>
                                            Navigator.of(context).pop(template),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(12),
                                            decoration: BoxDecoration(
                                              color: Colors.orange.shade50,
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Icon(
                                              Icons.description,
                                              color: Colors.orange.shade600,
                                              size: 24,
                                            ),
                                          ),
                                          const SizedBox(width: 16),

                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  template.name,
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 12.0,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  template.description,
                                                  style: TextStyle(
                                                    color: Colors.grey.shade600,
                                                    fontSize: 10.0,
                                                  ),
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                const SizedBox(height: 8),
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons.access_time,
                                                      size: 14,
                                                      color:
                                                          Colors.grey.shade500,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      'Last used: ${_formatDate(template.lastUsed)}',
                                                      style: TextStyle(
                                                        fontSize: 8.0,
                                                        color:
                                                            Colors
                                                                .grey
                                                                .shade500,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),

                                          Icon(
                                            Icons.arrow_forward_ios,
                                            color: Colors.grey.shade400,
                                            size: 16,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
        );

        if (selectedTemplate != null) {
          await _loadTemplateData(selectedTemplate);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading templates: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Map<String, dynamic> _collectCurrentFormData() {
    final wallMotionScoresMap = <String, String>{};
    _wallMotionScores.forEach((segment, score) {
      wallMotionScoresMap[segment.toString().split('.').last] =
          score.toString().split('.').last;
    });

    final valveMorphologyMap = <String, String>{};
    _valveMorphology.forEach((valve, abnormality) {
      valveMorphologyMap[valve] = abnormality.toString().split('.').last;
    });

    final valveMorphologyOtherMap = <String, String>{};
    _valveMorphologyOtherControllers.forEach((valve, controller) {
      valveMorphologyOtherMap[valve] = controller.text;
    });

    final valveStenosisMap = <String, String>{};
    _valveStenosis.forEach((valve, severity) {
      valveStenosisMap[valve] = severity.toString().split('.').last;
    });

    final valveRegurgitationMap = <String, String>{};
    _valveRegurgitation.forEach((valve, severity) {
      valveRegurgitationMap[valve] = severity.toString().split('.').last;
    });

    return {
      'patientInfo': {
        'name': _patientNameController.text,
        'gender': _selectedGender?.toString().split('.').last,
        'age':
            _ageController.text.isNotEmpty
                ? int.tryParse(_ageController.text)
                : null,
        'priority': _selectedPriority.toString().split('.').last,
        'indication': _indicationController.text,
        'location': _selectedLocation.toString().split('.').last,
        'requestedBy':
            _selectedPriority == Priority.requested
                ? _requestedByController.text
                : null,
        'height':
            _heightController.text.isNotEmpty
                ? double.tryParse(_heightController.text)
                : null,
        'weight':
            _weightController.text.isNotEmpty
                ? double.tryParse(_weightController.text)
                : null,
      },
      'echoParameters': {
        'lvedd':
            _lveddController.text.isNotEmpty
                ? double.tryParse(_lveddController.text)
                : null,
        'lvesd':
            _lvesdController.text.isNotEmpty
                ? double.tryParse(_lvesdController.text)
                : null,
        'ivsd':
            _ivsdController.text.isNotEmpty
                ? double.tryParse(_ivsdController.text)
                : null,
        'lvpw':
            _lvpwController.text.isNotEmpty
                ? double.tryParse(_lvpwController.text)
                : null,
        'relativeWallThickness':
            _relativeWallThicknessController.text.isNotEmpty
                ? double.tryParse(_relativeWallThicknessController.text)
                : null,
        'la':
            _laController.text.isNotEmpty
                ? double.tryParse(_laController.text)
                : null,
        'ao':
            _aoController.text.isNotEmpty
                ? double.tryParse(_aoController.text)
                : null,
        'ef':
            _efController.text.isNotEmpty
                ? double.tryParse(_efController.text)
                : null,
        'fs':
            _fsController.text.isNotEmpty
                ? double.tryParse(_fsController.text)
                : null,
        'diastolicGrade': _selectedDiastolicGrade,
        'tapse':
            _tapseController.text.isNotEmpty
                ? double.tryParse(_tapseController.text)
                : null,
        'pasp':
            _paspController.text.isNotEmpty
                ? double.tryParse(_paspController.text)
                : null,
        'rvSize': _rvSize.toString().split('.').last,
        'raSize': _raSize.toString().split('.').last,
        'rvBase':
            _rvBaseController.text.isNotEmpty
                ? double.tryParse(_rvBaseController.text)
                : null,
        'rvMid':
            _rvMidController.text.isNotEmpty
                ? double.tryParse(_rvMidController.text)
                : null,
        'rvLength':
            _rvLengthController.text.isNotEmpty
                ? double.tryParse(_rvLengthController.text)
                : null,
        'rvWallThickness':
            _rvWallThicknessController.text.isNotEmpty
                ? double.tryParse(_rvWallThicknessController.text)
                : null,
      },
      'wallMotion': wallMotionScoresMap,
      'valves': {
        'morphology': valveMorphologyMap,
        'morphologyOther': valveMorphologyOtherMap,
        'stenosis': valveStenosisMap,
        'regurgitation': valveRegurgitationMap,
        'mitralRhythm': _mitralValveRhythm.toString().split('.').last,
      },
      'pericardium': {
        'effusionSeverity':
            _pericardialEffusionSeverity.toString().split('.').last,
        'effusionLocation':
            _pericardialEffusionLocation.toString().split('.').last,
      },
      'ivc': {
        'diameter': _ivcDiameterController.text,
        'collapsibility': _ivcCollapsibility.toString().split('.').last,
      },
      'unusualFindings': {
        'septalDeformity': _septalDeformity.toString().split('.').last,
        'finding': _unusualFinding.toString().split('.').last,
        'location': _unusualFindingLocationController.text,
        'description': _unusualFindingDescriptionController.text,
      },
    };
  }

  Future<void> _loadTemplateData(SmartTemplate template) async {
    try {
      final data = template.templateData;

      if (data['patientInfo'] != null) {
        final patientInfo = data['patientInfo'];
        _patientNameController.text = patientInfo['name'] ?? '';
        _ageController.text = patientInfo['age']?.toString() ?? '';
        _heightController.text = patientInfo['height']?.toString() ?? '';
        _weightController.text = patientInfo['weight']?.toString() ?? '';
        _indicationController.text = patientInfo['indication'] ?? '';
        _requestedByController.text = patientInfo['requestedBy'] ?? '';

        if (patientInfo['gender'] != null) {
          _selectedGender = Gender.values.firstWhere(
            (g) => g.toString().split('.').last == patientInfo['gender'],
            orElse: () => Gender.male,
          );
        }

        if (patientInfo['priority'] != null) {
          _selectedPriority = Priority.values.firstWhere(
            (p) => p.toString().split('.').last == patientInfo['priority'],
            orElse: () => Priority.routine,
          );
        }

        if (patientInfo['location'] != null) {
          _selectedLocation = Location.values.firstWhere(
            (l) => l.toString().split('.').last == patientInfo['location'],
            orElse: () => Location.outpatient,
          );
        }
      }

      if (data['echoParameters'] != null) {
        final echoParams = data['echoParameters'];
        _lveddController.text = echoParams['lvedd']?.toString() ?? '';
        _lvesdController.text = echoParams['lvesd']?.toString() ?? '';
        _ivsdController.text = echoParams['ivsd']?.toString() ?? '';
        _lvpwController.text = echoParams['lvpw']?.toString() ?? '';
        _relativeWallThicknessController.text =
            echoParams['relativeWallThickness']?.toString() ?? '';
        _laController.text = echoParams['la']?.toString() ?? '';
        _aoController.text = echoParams['ao']?.toString() ?? '';
        _efController.text = echoParams['ef']?.toString() ?? '';
        _fsController.text = echoParams['fs']?.toString() ?? '';
        _tapseController.text = echoParams['tapse']?.toString() ?? '';
        _paspController.text = echoParams['pasp']?.toString() ?? '';

        if (echoParams['diastolicGrade'] != null) {
          _selectedDiastolicGrade = echoParams['diastolicGrade'];
        }
      }

      if (data['ivc'] != null) {
        final ivcData = data['ivc'];
        _ivcDiameterController.text = ivcData['diameter']?.toString() ?? '';

        if (ivcData['collapsibility'] != null) {
          _ivcCollapsibility = IVCCollapsibility.values.firstWhere(
            (c) => c.toString().split('.').last == ivcData['collapsibility'],
            orElse: () => IVCCollapsibility.normal,
          );
        }
      }

      if (data['wallMotion'] != null) {
        final wallMotionData = data['wallMotion'];
        wallMotionData.forEach((segmentKey, scoreValue) {
          final segment = LVSegment.values.firstWhere(
            (s) => s.toString().split('.').last == segmentKey,
            orElse: () => LVSegment.basalAnterior,
          );

          final score = WallMotionScore.values.firstWhere(
            (s) => s.toString().split('.').last == scoreValue,
            orElse: () => WallMotionScore.normal,
          );

          _wallMotionScores[segment] = score;
        });
      }

      if (data['valves'] != null) {
        final valveData = data['valves'];

        if (valveData['morphology'] != null) {
          final morphologyData = valveData['morphology'];
          morphologyData.forEach((valve, abnormalityValue) {
            final abnormality = ValveStructuralAbnormality.values.firstWhere(
              (a) => a.toString().split('.').last == abnormalityValue,
              orElse: () => ValveStructuralAbnormality.normal,
            );
            _valveMorphology[valve] = abnormality;
          });
        }

        if (valveData['stenosis'] != null) {
          final stenosisData = valveData['stenosis'];
          stenosisData.forEach((valve, severityValue) {
            final severity = StenosisSeverity.values.firstWhere(
              (s) => s.toString().split('.').last == severityValue,
              orElse: () => StenosisSeverity.none,
            );
            _valveStenosis[valve] = severity;
          });
        }

        if (valveData['regurgitation'] != null) {
          final regurgitationData = valveData['regurgitation'];
          regurgitationData.forEach((valve, severityValue) {
            final severity = RegurgitationSeverity.values.firstWhere(
              (s) => s.toString().split('.').last == severityValue,
              orElse: () => RegurgitationSeverity.none,
            );
            _valveRegurgitation[valve] = severity;
          });
        }
      }

      if (data['pericardium'] != null) {
        final pericardiumData = data['pericardium'];

        if (pericardiumData['effusionSeverity'] != null) {
          _pericardialEffusionSeverity = PericardialEffusionSeverity.values
              .firstWhere(
                (s) =>
                    s.toString().split('.').last ==
                    pericardiumData['effusionSeverity'],
                orElse: () => PericardialEffusionSeverity.none,
              );
        }

        if (pericardiumData['effusionLocation'] != null) {
          _pericardialEffusionLocation = PericardialEffusionLocation.values
              .firstWhere(
                (l) =>
                    l.toString().split('.').last ==
                    pericardiumData['effusionLocation'],
                orElse: () => PericardialEffusionLocation.circumferential,
              );
        }
      }

      if (data['unusualFindings'] != null) {
        final unusualData = data['unusualFindings'];

        if (unusualData['septalDeformity'] != null) {
          _septalDeformity = SeptalDeformity.values.firstWhere(
            (d) =>
                d.toString().split('.').last == unusualData['septalDeformity'],
            orElse: () => SeptalDeformity.none,
          );
        }

        if (unusualData['finding'] != null) {
          _unusualFinding = UnusualFinding.values.firstWhere(
            (f) => f.toString().split('.').last == unusualData['finding'],
            orElse: () => UnusualFinding.none,
          );
        }

        _unusualFindingLocationController.text = unusualData['location'] ?? '';
        _unusualFindingDescriptionController.text =
            unusualData['description'] ?? '';
      }

      await SmartTemplateService.updateLastUsed(template.id);

      setState(() {});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Template "${template.name}" loaded successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading template: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quick Report'),
        elevation: 4,
        backgroundColor: _primaryColor,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              if (value == 'save_template') {
                _showSaveTemplateDialog();
              } else if (value == 'load_template') {
                _showLoadTemplateDialog();
              }
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'load_template',
                    child: Row(
                      children: [
                        Icon(
                          Icons.download,
                          size: 16,
                          color: Colors.orange.shade600,
                        ),
                        const SizedBox(width: 8),
                        const Text('Load Template'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'save_template',
                    child: Row(
                      children: [
                        Icon(
                          Icons.save,
                          size: 16,
                          color: Colors.orange.shade600,
                        ),
                        const SizedBox(width: 8),
                        const Text('Save as Template'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(color: _backgroundColor),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  elevation: 10,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: const BorderSide(color: _fillColor, width: 1.5),
                  ),
                  shadowColor: _shadowColor.withAlpha(80),
                  child: Padding(
                    padding: const EdgeInsets.all(18.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: _buildSectionHeader(
                            'Patient Information',
                            Icons.person,
                          ),
                        ),
                        const SizedBox(height: 20),

                        TextFormField(
                          controller: _patientNameController,
                          decoration: _createPatientInfoInputDecoration(
                            'Patient Name',
                            Icons.person_outline,
                          ).copyWith(
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 18,
                            ),
                          ),
                          style: const TextStyle(
                            fontSize: FontSizes.heading2,
                            color: _textColor,
                          ),

                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter patient name';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () => _selectDate(context),
                                child: InputDecorator(
                                  decoration: _createPatientInfoInputDecoration(
                                    'Date',
                                    Icons.calendar_today,
                                  ).copyWith(
                                    suffixIcon: Icon(
                                      Icons.arrow_drop_down,
                                      color: _primaryColor,
                                    ),

                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 18,
                                    ),
                                  ),
                                  child: Text(
                                    '${_selectedDate.day.toString().padLeft(2, '0')}/${_selectedDate.month.toString().padLeft(2, '0')}/${_selectedDate.year}',
                                    style: const TextStyle(
                                      fontSize: FontSizes.heading2,
                                      color: _textColor,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),

                            Expanded(
                              child: InkWell(
                                onTap: () => _selectTime(context),
                                child: InputDecorator(
                                  decoration: _createPatientInfoInputDecoration(
                                    'Time',
                                    Icons.access_time,
                                  ).copyWith(
                                    suffixIcon: Icon(
                                      Icons.arrow_drop_down,
                                      color: _primaryColor,
                                    ),

                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 18,
                                    ),
                                  ),
                                  child: Text(
                                    '${_selectedTime.hour.toString().padLeft(2, '0')}:${_selectedTime.minute.toString().padLeft(2, '0')}',
                                    style: const TextStyle(
                                      fontSize: FontSizes.heading2,
                                      color: _textColor,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField<Gender>(
                                decoration: _createPatientInfoInputDecoration(
                                  'Gender',
                                  Icons.wc,
                                ),
                                dropdownColor: _fillColor,
                                iconEnabledColor: _primaryColor,
                                isDense: true,
                                itemHeight: 48,
                                isExpanded: true,
                                style: const TextStyle(
                                  color: _textColor,
                                  fontSize: FontSizes.heading2,
                                ),
                                value: _selectedGender,
                                items:
                                    Gender.values.map((gender) {
                                      return DropdownMenuItem<Gender>(
                                        value: gender,
                                        child: Text(
                                          gender.toString().split('.').last,
                                          style: TextStyle(
                                            color:
                                                _selectedGender == gender
                                                    ? _primaryColor
                                                    : _textColor,
                                            fontSize: FontSizes.dropdownItem,
                                            fontWeight:
                                                _selectedGender == gender
                                                    ? FontWeight.bold
                                                    : FontWeight.normal,
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                onChanged: (Gender? value) {
                                  setState(() {
                                    _selectedGender = value;
                                  });
                                },
                                validator: (value) {
                                  if (value == null) {
                                    return 'Please select gender';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 12),

                            Expanded(
                              child: TextFormField(
                                controller: _ageController,
                                decoration: _createPatientInfoInputDecoration(
                                  'Age (years)',
                                  Icons.cake,
                                ).copyWith(
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 18,
                                  ),
                                ),
                                style: const TextStyle(
                                  fontSize: FontSizes.heading2,
                                  color: _textColor,
                                ),
                                keyboardType: TextInputType.number,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter age';
                                  }
                                  if (int.tryParse(value) == null) {
                                    return 'Please enter a valid number';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _heightController,
                                decoration: _createPatientInfoInputDecoration(
                                  'Height (cm)',
                                  Icons.height,
                                ).copyWith(
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 18,
                                  ),
                                  helperText: 'For BSA calculation only',
                                  helperStyle: TextStyle(
                                    color: _helperTextColor,
                                    fontSize: FontSizes.helperText,
                                  ),
                                ),
                                style: const TextStyle(
                                  fontSize: FontSizes.heading2,
                                  color: _textColor,
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 12),

                            Expanded(
                              child: TextFormField(
                                controller: _weightController,
                                decoration: _createPatientInfoInputDecoration(
                                  'Weight (kg)',
                                  Icons.monitor_weight_outlined,
                                ).copyWith(
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 18,
                                  ),
                                  helperText: 'For BSA calculation only',
                                  helperStyle: TextStyle(
                                    color: _helperTextColor,
                                    fontSize: FontSizes.helperText,
                                  ),
                                ),
                                style: const TextStyle(
                                  fontSize: FontSizes.heading2,
                                  color: _textColor,
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField<Priority>(
                                decoration: _createPatientInfoInputDecoration(
                                  'Priority',
                                  Icons.priority_high,
                                ),
                                dropdownColor: _fillColor,
                                iconEnabledColor: _primaryColor,
                                isDense: true,
                                itemHeight: 48,
                                isExpanded: true,
                                style: const TextStyle(
                                  color: _textColor,
                                  fontSize: FontSizes.heading2,
                                ),
                                value: _selectedPriority,
                                items:
                                    Priority.values.map((priority) {
                                      String displayText;
                                      Color textColor;
                                      switch (priority) {
                                        case Priority.routine:
                                          displayText = 'Routine';
                                          textColor = _successColor;
                                          break;
                                        case Priority.urgent:
                                          displayText = 'Urgent';
                                          textColor = _warningColor;
                                          break;
                                        case Priority.critical:
                                          displayText = 'Critical';
                                          textColor = _errorColor;
                                          break;
                                        case Priority.followUp:
                                          displayText = 'Follow-up';
                                          textColor = _infoColor;
                                          break;
                                        case Priority.requested:
                                          displayText = 'Requested';
                                          textColor = _purpleColor;
                                          break;
                                      }
                                      return DropdownMenuItem<Priority>(
                                        value: priority,
                                        child: Text(
                                          displayText,
                                          style: TextStyle(
                                            color: textColor,
                                            fontSize: FontSizes.dropdownItem,
                                            fontWeight:
                                                _selectedPriority == priority
                                                    ? FontWeight.bold
                                                    : FontWeight.normal,
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                onChanged: (Priority? value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedPriority = value;
                                      _showRequestedByField =
                                          (value == Priority.requested);
                                    });
                                  }
                                },
                              ),
                            ),
                            const SizedBox(width: 12),

                            Expanded(
                              child: DropdownButtonFormField<Location>(
                                decoration: _createPatientInfoInputDecoration(
                                  'Location',
                                  Icons.location_on,
                                ),
                                dropdownColor: _fillColor,
                                iconEnabledColor: _primaryColor,
                                isDense: true,
                                itemHeight: 48,
                                isExpanded: true,
                                style: const TextStyle(
                                  color: _textColor,
                                  fontSize: FontSizes.heading2,
                                ),
                                value: _selectedLocation,
                                items:
                                    Location.values.map((location) {
                                      String displayText;
                                      switch (location) {
                                        case Location.inpatient:
                                          displayText = 'Inptn';
                                          break;
                                        case Location.outpatient:
                                          displayText = 'Outptn';
                                          break;
                                        case Location.emergency:
                                          displayText = 'ER';
                                          break;
                                        case Location.icu:
                                          displayText = 'ICU';
                                          break;
                                        case Location.ccu:
                                          displayText = 'CCU';
                                          break;
                                        case Location.other:
                                          displayText = 'Clinic';
                                          break;
                                      }
                                      return DropdownMenuItem<Location>(
                                        value: location,
                                        child: Text(
                                          displayText,
                                          style: TextStyle(
                                            color:
                                                _selectedLocation == location
                                                    ? _primaryColor
                                                    : _textColor,
                                            fontSize: FontSizes.dropdownItem,
                                            fontWeight:
                                                _selectedLocation == location
                                                    ? FontWeight.bold
                                                    : FontWeight.normal,
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                onChanged: (Location? value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedLocation = value;
                                    });
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        if (_showRequestedByField) ...[
                          TextFormField(
                            controller: _requestedByController,
                            decoration: _createPatientInfoInputDecoration(
                              'Requested by',
                              Icons.person,
                            ).copyWith(
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 18,
                              ),
                            ),
                            style: const TextStyle(
                              fontSize: FontSizes.heading2,
                              color: _textColor,
                            ),
                          ),
                          const SizedBox(height: 16),
                        ],

                        TextFormField(
                          controller: _indicationController,
                          decoration: _createPatientInfoInputDecoration(
                            'Indication',
                            Icons.description,
                          ).copyWith(
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 18,
                            ),
                          ),
                          style: const TextStyle(
                            fontSize: FontSizes.heading2,
                            color: _textColor,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                Card(
                  elevation: 10,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: const BorderSide(color: _fillColor, width: 1.5),
                  ),
                  shadowColor: _shadowColor.withAlpha(80),
                  child: Padding(
                    padding: const EdgeInsets.all(18.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: _buildSectionHeader(
                            'Echo Parameters',
                            Icons.favorite,
                          ),
                        ),
                        const SizedBox(height: 20),

                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _lvesdController,
                                decoration: _createEchoInputDecoration(
                                  'LVESD (mm)',
                                  'M:25-40 & F:22-35',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 12),

                            Expanded(
                              child: TextFormField(
                                controller: _lveddController,
                                decoration: _createEchoInputDecoration(
                                  'LVEDD (mm)',
                                  'M:42-58 & F:38-52',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _ivsdController,
                                decoration: _createEchoInputDecoration(
                                  'IVSd (mm)',
                                  '6-11',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 12),

                            Expanded(
                              child: TextFormField(
                                controller: _lvpwController,
                                decoration: _createEchoInputDecoration(
                                  'LVPWd (mm)',
                                  '6-10',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        TextFormField(
                          controller: _relativeWallThicknessController,
                          decoration: _createEchoInputDecoration(
                            'Relative Wall Thickness',
                            '0.32-0.42',
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _laController,
                                decoration: _createEchoInputDecoration(
                                  'LA (mm)',
                                  'M:30-40 & F:27-38',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 12),

                            Expanded(
                              child: TextFormField(
                                controller: _aoController,
                                decoration: _createEchoInputDecoration(
                                  'AO (mm)',
                                  '20-40',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        TextFormField(
                          controller: _efController,
                          decoration: _createEchoInputDecoration(
                            'EF (%)',
                            'M:52-72 & F:54-74',
                          ),
                          keyboardType: TextInputType.number,
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                decoration: InputDecoration(
                                  labelText: 'Diastolic Dysfunction',
                                  labelStyle: TextStyle(
                                    color: _primaryColor,
                                    fontSize: FontSizes.labelLarge,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(color: _borderColor),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(color: _borderColor),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(
                                      color: _primaryColor,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: _fillColor,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 16,
                                  ),
                                ),
                                isDense: true,
                                itemHeight: 48,
                                isExpanded: true,
                                style: const TextStyle(
                                  color: _textColor,
                                  fontSize: FontSizes.heading2,
                                ),
                                value: _selectedDiastolicGrade,
                                items:
                                    [
                                          'No',
                                          'Grade I',
                                          'Grade II',
                                          'Grade III',
                                          'Grade IV',
                                        ]
                                        .map(
                                          (grade) => DropdownMenuItem<String>(
                                            value: grade,
                                            child: Text(
                                              grade,
                                              style: TextStyle(
                                                fontSize:
                                                    FontSizes.dropdownItem,
                                                fontWeight:
                                                    _selectedDiastolicGrade ==
                                                            grade
                                                        ? FontWeight.bold
                                                        : FontWeight.normal,
                                              ),
                                            ),
                                          ),
                                        )
                                        .toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedDiastolicGrade = value;
                                    });
                                  }
                                },
                              ),
                            ),
                            const SizedBox(width: 12),

                            Expanded(
                              child: TextFormField(
                                controller: _fsController,
                                decoration: _createEchoInputDecoration(
                                  'FS (%)',
                                  '25-45',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _tapseController,
                                decoration: _createEchoInputDecoration(
                                  'TAPSE (mm)',
                                  '17-25',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 12),

                            Expanded(
                              child: TextFormField(
                                controller: _paspController,
                                decoration: _createEchoInputDecoration(
                                  'PASP (mmHg)',
                                  '17-30',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField<RVSize>(
                                decoration: InputDecoration(
                                  labelText: 'RV Size',
                                  labelStyle: TextStyle(
                                    color: _primaryColor,
                                    fontSize: FontSizes.labelLarge,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(color: _borderColor),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(color: _borderColor),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(
                                      color: _primaryColor,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: _fillColor,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 16,
                                  ),
                                  isDense: true,
                                ),
                                isExpanded: true,
                                value: _rvSize,
                                items:
                                    RVSize.values.map((size) {
                                      Color textColor =
                                          size == RVSize.normal
                                              ? _successColor
                                              : _errorColor;

                                      return DropdownMenuItem<RVSize>(
                                        value: size,
                                        child: Text(
                                          size == RVSize.normal
                                              ? 'Normal'
                                              : 'Dilated',
                                          style: TextStyle(
                                            color: textColor,
                                            fontSize: FontSizes.dropdownItem,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                onChanged: (RVSize? value) {
                                  if (value != null) {
                                    setState(() {
                                      _rvSize = value;
                                    });
                                  }
                                },
                              ),
                            ),
                            const SizedBox(width: 12),

                            Expanded(
                              child: DropdownButtonFormField<RASize>(
                                decoration: InputDecoration(
                                  labelText: 'RA Size',
                                  labelStyle: TextStyle(
                                    color: _primaryColor,
                                    fontSize: FontSizes.labelLarge,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(color: _borderColor),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(color: _borderColor),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(
                                      color: _primaryColor,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: _fillColor,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 16,
                                  ),
                                  isDense: true,
                                ),
                                isExpanded: true,
                                value: _raSize,
                                items:
                                    RASize.values.map((size) {
                                      Color textColor =
                                          size == RASize.normal
                                              ? _successColor
                                              : _errorColor;

                                      return DropdownMenuItem<RASize>(
                                        value: size,
                                        child: Text(
                                          size == RASize.normal
                                              ? 'Normal'
                                              : 'Dilated',
                                          style: TextStyle(
                                            color: textColor,
                                            fontSize: FontSizes.dropdownItem,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                onChanged: (RASize? value) {
                                  if (value != null) {
                                    setState(() {
                                      _raSize = value;
                                    });
                                  }
                                },
                              ),
                            ),
                          ],
                        ),

                        if (_rvSize == RVSize.dilated) ...[
                          const SizedBox(height: 16),

                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _rvBaseController,
                                  decoration: _createEchoInputDecoration(
                                    'RV Base (mm)',
                                    '25-41',
                                  ),
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                              const SizedBox(width: 12),

                              Expanded(
                                child: TextFormField(
                                  controller: _rvMidController,
                                  decoration: _createEchoInputDecoration(
                                    'RV Mid (mm)',
                                    '19-35',
                                  ),
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _rvLengthController,
                                  decoration: _createEchoInputDecoration(
                                    'RV Length (mm)',
                                    '59-83',
                                  ),
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                              const SizedBox(width: 12),

                              Expanded(
                                child: TextFormField(
                                  controller: _rvWallThicknessController,
                                  decoration: _createEchoInputDecoration(
                                    'RV Wall (mm)',
                                    '1-5',
                                  ),
                                  keyboardType: TextInputType.number,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                Card(
                  elevation: 10,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: const BorderSide(color: _fillColor, width: 1.5),
                  ),
                  shadowColor: _shadowColor.withAlpha(80),
                  child: Padding(
                    padding: const EdgeInsets.all(18.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: _buildSectionHeader(
                            'Wall Motion',
                            Icons.favorite_border,
                          ),
                        ),
                        const SizedBox(height: 20),

                        Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField<
                                WallMotionAbnormality
                              >(
                                decoration: InputDecoration(
                                  labelText: 'Wall Motion Abnormality',
                                  labelStyle: TextStyle(
                                    color: _primaryColor,
                                    fontSize: FontSizes.labelLarge,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(color: _borderColor),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(color: _borderColor),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    borderSide: BorderSide(
                                      color: _primaryColor,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: _fillColor,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 16,
                                  ),
                                  isDense: true,
                                ),
                                isExpanded: true,
                                value: _wallMotionAbnormality,
                                items:
                                    WallMotionAbnormality.values.map((
                                      abnormality,
                                    ) {
                                      return DropdownMenuItem<
                                        WallMotionAbnormality
                                      >(
                                        value: abnormality,
                                        child: Text(
                                          abnormality ==
                                                  WallMotionAbnormality.yes
                                              ? 'Yes'
                                              : 'No',
                                          style: TextStyle(
                                            color:
                                                abnormality ==
                                                        WallMotionAbnormality
                                                            .yes
                                                    ? _errorColor
                                                    : _successColor,
                                            fontSize: FontSizes.dropdownItem,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                onChanged: (WallMotionAbnormality? value) {
                                  if (value != null) {
                                    setState(() {
                                      _wallMotionAbnormality = value;

                                      if (value == WallMotionAbnormality.no) {
                                        _wallMotionScores.forEach((segment, _) {
                                          _wallMotionScores[segment] =
                                              WallMotionScore.normal;
                                        });
                                      }
                                    });
                                  }
                                },
                              ),
                            ),
                          ],
                        ),

                        if (_wallMotionAbnormality ==
                            WallMotionAbnormality.yes) ...[
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: DropdownButtonFormField<
                                  WallMotionAbnormalityType
                                >(
                                  decoration: InputDecoration(
                                    labelText: 'Abnormality Type',
                                    labelStyle: TextStyle(
                                      color: _primaryColor,
                                      fontSize: FontSizes.labelLarge,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                        color: _borderColor,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                        color: _borderColor,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                        color: _primaryColor,
                                        width: 2,
                                      ),
                                    ),
                                    filled: true,
                                    fillColor: _fillColor,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 16,
                                    ),
                                    isDense: true,
                                  ),
                                  isExpanded: true,
                                  value: _wallMotionAbnormalityType,
                                  items:
                                      WallMotionAbnormalityType.values.map((
                                        type,
                                      ) {
                                        return DropdownMenuItem<
                                          WallMotionAbnormalityType
                                        >(
                                          value: type,
                                          child: Text(
                                            type ==
                                                    WallMotionAbnormalityType
                                                        .global
                                                ? 'Global'
                                                : 'Segmental',
                                            style: const TextStyle(
                                              color: _textColor,
                                              fontSize: FontSizes.dropdownItem,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                  onChanged: (
                                    WallMotionAbnormalityType? value,
                                  ) {
                                    if (value != null) {
                                      setState(() {
                                        _wallMotionAbnormalityType = value;

                                        if (value ==
                                            WallMotionAbnormalityType.global) {
                                          _wallMotionScores.forEach((
                                            segment,
                                            _,
                                          ) {
                                            _wallMotionScores[segment] =
                                                WallMotionScore.hypokinetic;
                                          });
                                        }
                                      });
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],

                        if (_wallMotionAbnormality ==
                                WallMotionAbnormality.yes &&
                            _wallMotionAbnormalityType ==
                                WallMotionAbnormalityType.segmental) ...[
                          const SizedBox(height: 20),

                          const Text(
                            'Basal Segments',
                            style: TextStyle(
                              fontSize: FontSizes.heading2,
                              fontWeight: FontWeight.bold,
                              color: _textColor,
                            ),
                          ),
                          const SizedBox(height: 12),
                          LayoutBuilder(
                            builder: (context, constraints) {
                              int crossAxisCount = 2;
                              double childAspectRatio = 2.0;

                              if (constraints.maxWidth > 800) {
                                crossAxisCount = 3;
                                childAspectRatio = 2.3;
                              }
                              if (constraints.maxWidth > 1200) {
                                crossAxisCount = 4;
                                childAspectRatio = 2.5;
                              }

                              return GridView.count(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                crossAxisCount: crossAxisCount,
                                childAspectRatio: childAspectRatio,
                                crossAxisSpacing: 12,
                                mainAxisSpacing: 12,
                                children: [
                                  _createWallMotionDropdown(
                                    LVSegment.basalAnterior,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.basalAnteroseptal,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.basalInferoseptal,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.basalInferior,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.basalInferolateral,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.basalAnterolateral,
                                  ),
                                ],
                              );
                            },
                          ),

                          const SizedBox(height: 20),

                          const Text(
                            'Mid Segments',
                            style: TextStyle(
                              fontSize: FontSizes.heading2,
                              fontWeight: FontWeight.bold,
                              color: _textColor,
                            ),
                          ),
                          const SizedBox(height: 12),
                          LayoutBuilder(
                            builder: (context, constraints) {
                              int crossAxisCount = 2;
                              double childAspectRatio = 2.0;

                              if (constraints.maxWidth > 800) {
                                crossAxisCount = 3;
                                childAspectRatio = 2.3;
                              }
                              if (constraints.maxWidth > 1200) {
                                crossAxisCount = 4;
                                childAspectRatio = 2.5;
                              }

                              return GridView.count(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                crossAxisCount: crossAxisCount,
                                childAspectRatio: childAspectRatio,
                                crossAxisSpacing: 12,
                                mainAxisSpacing: 12,
                                children: [
                                  _createWallMotionDropdown(
                                    LVSegment.midAnterior,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.midAnteroseptal,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.midInferoseptal,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.midInferior,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.midInferolateral,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.midAnterolateral,
                                  ),
                                ],
                              );
                            },
                          ),

                          const SizedBox(height: 20),

                          const Text(
                            'Apical Segments',
                            style: TextStyle(
                              fontSize: FontSizes.heading2,
                              fontWeight: FontWeight.bold,
                              color: _textColor,
                            ),
                          ),
                          const SizedBox(height: 12),
                          LayoutBuilder(
                            builder: (context, constraints) {
                              int crossAxisCount = 2;
                              double childAspectRatio = 2.0;

                              if (constraints.maxWidth > 800) {
                                crossAxisCount = 4;
                                childAspectRatio = 2.3;
                              }

                              return GridView.count(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                crossAxisCount: crossAxisCount,
                                childAspectRatio: childAspectRatio,
                                crossAxisSpacing: 12,
                                mainAxisSpacing: 12,
                                children: [
                                  _createWallMotionDropdown(
                                    LVSegment.apicalAnterior,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.apicalSeptal,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.apicalInferior,
                                  ),
                                  _createWallMotionDropdown(
                                    LVSegment.apicalLateral,
                                  ),
                                ],
                              );
                            },
                          ),

                          const SizedBox(height: 20),

                          const Text(
                            'Apex',
                            style: TextStyle(
                              fontSize: FontSizes.heading2,
                              fontWeight: FontWeight.bold,
                              color: _textColor,
                            ),
                          ),
                          const SizedBox(height: 12),
                          LayoutBuilder(
                            builder: (context, constraints) {
                              double apexWidth = double.infinity;
                              if (constraints.maxWidth > 800) {
                                apexWidth = constraints.maxWidth / 4;
                              }

                              return SizedBox(
                                width: apexWidth,
                                child: _createWallMotionDropdown(
                                  LVSegment.apex,
                                ),
                              );
                            },
                          ),
                        ],

                        if (_wallMotionAbnormality ==
                                WallMotionAbnormality.yes &&
                            _wallMotionAbnormalityType ==
                                WallMotionAbnormalityType.global) ...[
                          const SizedBox(height: 20),
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: _primaryColorLight,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(color: _borderColor),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.info_outline, color: _primaryColor),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'Global hypokinesia selected. All segments will be marked as hypokinetic.',
                                    style: TextStyle(
                                      fontSize: FontSizes.labelLarge,
                                      color: _primaryColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                Card(
                  elevation: 10,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: const BorderSide(color: _fillColor, width: 1.5),
                  ),
                  shadowColor: _shadowColor.withAlpha(80),
                  child: Padding(
                    padding: const EdgeInsets.all(18.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: _buildSectionHeader('Valves', Icons.waves),
                        ),
                        const SizedBox(height: 20),

                        const Text(
                          'Mitral Valve',
                          style: TextStyle(
                            fontSize: FontSizes.heading2,
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _createValveMorphologyDropdown('Mitral'),
                            ),
                            const SizedBox(width: 12),
                            Expanded(child: _createCardiacRhythmDropdown()),
                          ],
                        ),

                        if (_valveMorphology['Mitral'] ==
                            ValveStructuralAbnormality.other) ...[
                          const SizedBox(height: 12),
                          TextFormField(
                            controller:
                                _valveMorphologyOtherControllers['Mitral'],
                            decoration: InputDecoration(
                              labelText: 'Specify Other Morphology',
                              labelStyle: TextStyle(
                                color: _primaryColor,
                                fontSize: FontSizes.labelLarge,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: _primaryColor,
                                  width: 2,
                                ),
                              ),
                              filled: true,
                              fillColor: _fillColor,
                            ),
                          ),
                        ],
                        const SizedBox(height: 12),

                        if (_valveMorphology['Mitral'] ==
                            ValveStructuralAbnormality.prosthetic) ...[
                          Row(
                            children: [
                              Expanded(
                                child: _createProstheticValveTypeDropdown(
                                  'Mitral',
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _createProstheticValveFunctionDropdown(
                                  'Mitral',
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                        ],
                        Row(
                          children: [
                            Expanded(
                              child: _createValveStenosisDropdown('Mitral'),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _createValveRegurgitationDropdown(
                                'Mitral',
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        const Text(
                          'Aortic Valve',
                          style: TextStyle(
                            fontSize: FontSizes.heading2,
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _createValveMorphologyDropdown('Aortic'),
                            ),
                          ],
                        ),

                        if (_valveMorphology['Aortic'] ==
                            ValveStructuralAbnormality.other) ...[
                          const SizedBox(height: 12),
                          TextFormField(
                            controller:
                                _valveMorphologyOtherControllers['Aortic'],
                            decoration: InputDecoration(
                              labelText: 'Specify Other Morphology',
                              labelStyle: TextStyle(
                                color: _primaryColor,
                                fontSize: FontSizes.labelLarge,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: _primaryColor,
                                  width: 2,
                                ),
                              ),
                              filled: true,
                              fillColor: _fillColor,
                            ),
                          ),
                        ],
                        const SizedBox(height: 12),

                        if (_valveMorphology['Aortic'] ==
                            ValveStructuralAbnormality.prosthetic) ...[
                          Row(
                            children: [
                              Expanded(
                                child: _createProstheticValveTypeDropdown(
                                  'Aortic',
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _createProstheticValveFunctionDropdown(
                                  'Aortic',
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                        ],
                        Row(
                          children: [
                            Expanded(
                              child: _createValveStenosisDropdown('Aortic'),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _createValveRegurgitationDropdown(
                                'Aortic',
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        const Text(
                          'Tricuspid Valve',
                          style: TextStyle(
                            fontSize: FontSizes.heading2,
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _createValveMorphologyDropdown(
                                'Tricuspid',
                              ),
                            ),
                          ],
                        ),

                        if (_valveMorphology['Tricuspid'] ==
                            ValveStructuralAbnormality.other) ...[
                          const SizedBox(height: 12),
                          TextFormField(
                            controller:
                                _valveMorphologyOtherControllers['Tricuspid'],
                            decoration: InputDecoration(
                              labelText: 'Specify Other Morphology',
                              labelStyle: TextStyle(
                                color: _primaryColor,
                                fontSize: FontSizes.labelLarge,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: _primaryColor,
                                  width: 2,
                                ),
                              ),
                              filled: true,
                              fillColor: _fillColor,
                            ),
                          ),
                        ],
                        const SizedBox(height: 12),

                        if (_valveMorphology['Tricuspid'] ==
                            ValveStructuralAbnormality.prosthetic) ...[
                          Row(
                            children: [
                              Expanded(
                                child: _createProstheticValveTypeDropdown(
                                  'Tricuspid',
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _createProstheticValveFunctionDropdown(
                                  'Tricuspid',
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                        ],
                        Row(
                          children: [
                            Expanded(
                              child: _createValveStenosisDropdown('Tricuspid'),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _createValveRegurgitationDropdown(
                                'Tricuspid',
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        const Text(
                          'Pulmonary Valve',
                          style: TextStyle(
                            fontSize: FontSizes.heading2,
                            fontWeight: FontWeight.bold,
                            color: _textColor,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _createValveMorphologyDropdown(
                                'Pulmonary',
                              ),
                            ),
                          ],
                        ),

                        if (_valveMorphology['Pulmonary'] ==
                            ValveStructuralAbnormality.other) ...[
                          const SizedBox(height: 12),
                          TextFormField(
                            controller:
                                _valveMorphologyOtherControllers['Pulmonary'],
                            decoration: InputDecoration(
                              labelText: 'Specify Other Morphology',
                              labelStyle: TextStyle(
                                color: _primaryColor,
                                fontSize: FontSizes.labelLarge,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: _primaryColor,
                                  width: 2,
                                ),
                              ),
                              filled: true,
                              fillColor: _fillColor,
                            ),
                          ),
                        ],
                        const SizedBox(height: 12),

                        if (_valveMorphology['Pulmonary'] ==
                            ValveStructuralAbnormality.prosthetic) ...[
                          Row(
                            children: [
                              Expanded(
                                child: _createProstheticValveTypeDropdown(
                                  'Pulmonary',
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _createProstheticValveFunctionDropdown(
                                  'Pulmonary',
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                        ],
                        Row(
                          children: [
                            Expanded(
                              child: _createValveStenosisDropdown('Pulmonary'),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _createValveRegurgitationDropdown(
                                'Pulmonary',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                Card(
                  elevation: 10,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: const BorderSide(color: _fillColor, width: 1.5),
                  ),
                  shadowColor: _shadowColor.withAlpha(80),
                  child: Padding(
                    padding: const EdgeInsets.all(18.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: _buildSectionHeader(
                            'Pericardium',
                            Icons.water_drop_outlined,
                          ),
                        ),
                        const SizedBox(height: 20),

                        Row(
                          children: [
                            Expanded(
                              child:
                                  _createPericardialEffusionSeverityDropdown(),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child:
                                  _createPericardialEffusionLocationDropdown(),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                Card(
                  elevation: 10,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(color: _fillColor, width: 1.5),
                  ),
                  shadowColor: _shadowColor.withAlpha(80),
                  child: Padding(
                    padding: const EdgeInsets.all(18.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: _buildSectionHeader('IVC', Icons.line_axis),
                        ),
                        const SizedBox(height: 20),

                        TextFormField(
                          controller: _ivcDiameterController,
                          decoration: _createEchoInputDecoration(
                            'IVC Diameter (cm)',
                            '≤ 2.1 cm',
                          ),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'[0-9.]'),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        _createIVCCollapsibilityDropdown(),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                Card(
                  elevation: 10,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: const BorderSide(color: _fillColor, width: 1.5),
                  ),
                  shadowColor: _shadowColor.withAlpha(80),
                  child: Padding(
                    padding: const EdgeInsets.all(18.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: _buildSectionHeader(
                            'Unusual Findings',
                            Icons.warning_amber_rounded,
                          ),
                        ),
                        const SizedBox(height: 20),

                        _createSeptalDeformityDropdown(),
                        const SizedBox(height: 16),

                        _createUnusualFindingDropdown(),
                        const SizedBox(height: 16),

                        if (_unusualFinding != UnusualFinding.none) ...[
                          TextFormField(
                            controller: _unusualFindingLocationController,
                            decoration: InputDecoration(
                              labelText: 'Location',
                              labelStyle: TextStyle(
                                color: _primaryColor,
                                fontSize: FontSizes.labelLarge,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: _primaryColor,
                                  width: 2,
                                ),
                              ),
                              filled: true,
                              fillColor: _fillColor,
                            ),
                          ),
                          const SizedBox(height: 16),

                          TextFormField(
                            controller: _unusualFindingDescriptionController,
                            decoration: InputDecoration(
                              labelText: 'Description',
                              labelStyle: TextStyle(
                                color: _primaryColor,
                                fontSize: FontSizes.labelLarge,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(color: _borderColor),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide(
                                  color: _primaryColor,
                                  width: 2,
                                ),
                              ),
                              filled: true,
                              fillColor: _fillColor,
                            ),

                            maxLines: 3,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.description),
                    label: const Text(
                      'Generate Report',
                      style: TextStyle(
                        fontSize: FontSizes.buttonLarge,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    onPressed: () async {
                      if (_formKey.currentState!.validate()) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Quick report generated'),
                          ),
                        );

                        final patientName = _patientNameController.text;
                        final gender =
                            _selectedGender?.toString().split('.').last;
                        final age =
                            _ageController.text.isNotEmpty
                                ? int.tryParse(_ageController.text)
                                : null;

                        final lvedd =
                            _lveddController.text.isNotEmpty
                                ? double.tryParse(_lveddController.text)
                                : null;
                        final lvesd =
                            _lvesdController.text.isNotEmpty
                                ? double.tryParse(_lvesdController.text)
                                : null;
                        final ivsd =
                            _ivsdController.text.isNotEmpty
                                ? double.tryParse(_ivsdController.text)
                                : null;
                        final lvpw =
                            _lvpwController.text.isNotEmpty
                                ? double.tryParse(_lvpwController.text)
                                : null;
                        final la =
                            _laController.text.isNotEmpty
                                ? double.tryParse(_laController.text)
                                : null;
                        final ao =
                            _aoController.text.isNotEmpty
                                ? double.tryParse(_aoController.text)
                                : null;
                        final ef =
                            _efController.text.isNotEmpty
                                ? double.tryParse(_efController.text)
                                : null;
                        final fs =
                            _fsController.text.isNotEmpty
                                ? double.tryParse(_fsController.text)
                                : null;
                        final tapse =
                            _tapseController.text.isNotEmpty
                                ? double.tryParse(_tapseController.text)
                                : null;
                        final pasp =
                            _paspController.text.isNotEmpty
                                ? double.tryParse(_paspController.text)
                                : null;

                        final Map<String, String> wallMotionScoresMap = {};

                        wallMotionScoresMap['abnormality'] =
                            _wallMotionAbnormality.toString().split('.').last;

                        if (_wallMotionAbnormality ==
                            WallMotionAbnormality.yes) {
                          wallMotionScoresMap['abnormalityType'] =
                              _wallMotionAbnormalityType
                                  .toString()
                                  .split('.')
                                  .last;
                        }

                        if (_wallMotionAbnormality ==
                            WallMotionAbnormality.yes) {
                          if (_wallMotionAbnormalityType ==
                              WallMotionAbnormalityType.global) {
                            _wallMotionScores.forEach((segment, score) {
                              wallMotionScoresMap[segment.displayName] =
                                  score.toString().split('.').last;
                            });
                          } else {
                            _wallMotionScores.forEach((segment, score) {
                              if (score != WallMotionScore.normal) {
                                wallMotionScoresMap[segment.displayName] =
                                    score.toString().split('.').last;
                              }
                            });
                          }
                        }

                        final Map<String, String> valveMorphologyMap = {};
                        final Map<String, String> valveMorphologyOtherMap = {};
                        _valveMorphology.forEach((valve, morphology) {
                          valveMorphologyMap[valve] =
                              morphology.toString().split('.').last;

                          if (morphology == ValveStructuralAbnormality.other) {
                            valveMorphologyOtherMap[valve] =
                                _valveMorphologyOtherControllers[valve]!.text;
                          }
                        });

                        final Map<String, String> valveStenosisMap = {};
                        _valveStenosis.forEach((valve, severity) {
                          valveStenosisMap[valve] =
                              severity.toString().split('.').last;
                        });

                        final Map<String, String> valveRegurgitationMap = {};
                        _valveRegurgitation.forEach((valve, severity) {
                          valveRegurgitationMap[valve] =
                              severity.toString().split('.').last;
                        });

                        double? bsa;
                        final String heightStr = _heightController.text;
                        final String weightStr = _weightController.text;

                        if (heightStr.isNotEmpty && weightStr.isNotEmpty) {
                          bsa = BSACalculator.calculateBSAFromString(
                            heightStr,
                            weightStr,
                          );
                        } else if (gender != null) {
                          bsa = BSACalculator.getDefaultBSA(gender);
                        }

                        final Map<String, dynamic> calculatedData = {
                          'patientInfo': {
                            'name': patientName,
                            'gender': gender,
                            'age': age,
                            'priority':
                                _selectedPriority.toString().split('.').last,
                            'indication': _indicationController.text,
                            'location':
                                _selectedLocation.toString().split('.').last,
                            'requestedBy':
                                _selectedPriority == Priority.requested
                                    ? _requestedByController.text
                                    : null,

                            'height':
                                heightStr.isNotEmpty
                                    ? double.tryParse(heightStr)
                                    : null,
                            'weight':
                                weightStr.isNotEmpty
                                    ? double.tryParse(weightStr)
                                    : null,
                            'bsa': bsa,
                          },
                          'echoParameters': {
                            'lvedd': lvedd,
                            'lvesd': lvesd,
                            'ivsd': ivsd,
                            'lvpw': lvpw,
                            'relativeWallThickness':
                                _relativeWallThicknessController.text.isNotEmpty
                                    ? double.tryParse(
                                      _relativeWallThicknessController.text,
                                    )
                                    : null,
                            'la': la,
                            'ao': ao,
                            'ef': ef,
                            'fs': fs,
                            'diastolicGrade': _selectedDiastolicGrade,
                            'tapse': tapse,
                            'pasp': pasp,
                            'rvSize': _rvSize.toString().split('.').last,
                            'raSize': _raSize.toString().split('.').last,
                            'rvBase':
                                _rvBaseController.text.isNotEmpty
                                    ? double.tryParse(_rvBaseController.text)
                                    : null,
                            'rvMid':
                                _rvMidController.text.isNotEmpty
                                    ? double.tryParse(_rvMidController.text)
                                    : null,
                            'rvLength':
                                _rvLengthController.text.isNotEmpty
                                    ? double.tryParse(_rvLengthController.text)
                                    : null,
                            'rvWallThickness':
                                _rvWallThicknessController.text.isNotEmpty
                                    ? double.tryParse(
                                      _rvWallThicknessController.text,
                                    )
                                    : null,
                          },
                          'wallMotion': wallMotionScoresMap,
                          'valves': {
                            'morphology': valveMorphologyMap,
                            'morphologyOther': valveMorphologyOtherMap,
                            'stenosis': valveStenosisMap,
                            'regurgitation': valveRegurgitationMap,
                            'mitralRhythm':
                                _mitralValveRhythm.toString().split('.').last,
                            'prosthetic': {
                              'mitral':
                                  _valveMorphology['Mitral'] ==
                                  ValveStructuralAbnormality.prosthetic,
                              'aortic':
                                  _valveMorphology['Aortic'] ==
                                  ValveStructuralAbnormality.prosthetic,
                              'tricuspid':
                                  _valveMorphology['Tricuspid'] ==
                                  ValveStructuralAbnormality.prosthetic,
                              'pulmonary':
                                  _valveMorphology['Pulmonary'] ==
                                  ValveStructuralAbnormality.prosthetic,
                            },
                            'prostheticTypes': {
                              'mitral':
                                  _valveMorphology['Mitral'] ==
                                          ValveStructuralAbnormality.prosthetic
                                      ? _prostheticValveTypes['Mitral']!
                                          .toString()
                                          .split('.')
                                          .last
                                      : null,
                              'aortic':
                                  _valveMorphology['Aortic'] ==
                                          ValveStructuralAbnormality.prosthetic
                                      ? _prostheticValveTypes['Aortic']!
                                          .toString()
                                          .split('.')
                                          .last
                                      : null,
                              'tricuspid':
                                  _valveMorphology['Tricuspid'] ==
                                          ValveStructuralAbnormality.prosthetic
                                      ? _prostheticValveTypes['Tricuspid']!
                                          .toString()
                                          .split('.')
                                          .last
                                      : null,
                              'pulmonary':
                                  _valveMorphology['Pulmonary'] ==
                                          ValveStructuralAbnormality.prosthetic
                                      ? _prostheticValveTypes['Pulmonary']!
                                          .toString()
                                          .split('.')
                                          .last
                                      : null,
                            },
                            'prostheticFunctions': {
                              'mitral':
                                  _valveMorphology['Mitral'] ==
                                          ValveStructuralAbnormality.prosthetic
                                      ? _prostheticValveFunction['Mitral']!
                                          .toString()
                                          .split('.')
                                          .last
                                      : null,
                              'aortic':
                                  _valveMorphology['Aortic'] ==
                                          ValveStructuralAbnormality.prosthetic
                                      ? _prostheticValveFunction['Aortic']!
                                          .toString()
                                          .split('.')
                                          .last
                                      : null,
                              'tricuspid':
                                  _valveMorphology['Tricuspid'] ==
                                          ValveStructuralAbnormality.prosthetic
                                      ? _prostheticValveFunction['Tricuspid']!
                                          .toString()
                                          .split('.')
                                          .last
                                      : null,
                              'pulmonary':
                                  _valveMorphology['Pulmonary'] ==
                                          ValveStructuralAbnormality.prosthetic
                                      ? _prostheticValveFunction['Pulmonary']!
                                          .toString()
                                          .split('.')
                                          .last
                                      : null,
                            },
                          },
                          'pericardium': {
                            'effusionSeverity':
                                _pericardialEffusionSeverity
                                    .toString()
                                    .split('.')
                                    .last,
                            'effusionLocation':
                                _pericardialEffusionLocation
                                    .toString()
                                    .split('.')
                                    .last,
                          },
                          'ivc': {
                            'diameter': _ivcDiameterController.text,
                            'collapsibility':
                                _ivcCollapsibility.toString().split('.').last,
                          },
                          'unusualFindings': {
                            'septalDeformity':
                                _septalDeformity.toString().split('.').last,
                            'finding':
                                _unusualFinding.toString().split('.').last,
                            'location': _unusualFindingLocationController.text,
                            'description':
                                _unusualFindingDescriptionController.text,
                          },
                        };

                        final interpretationService =
                            ModularInterpretationService();
                        final List<InterpretationSection>
                        interpretationSections = interpretationService
                            .generateInterpretationAsSections(calculatedData);

                        calculatedData['interpretation'] =
                            interpretationSections
                                .map(
                                  (s) => {
                                    'title': s.title,
                                    'content': s.content,
                                  },
                                )
                                .toList();
                        calculatedData['conclusion'] = '';

                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => QuickPdfPreviewScreen(
                                  patientData: calculatedData,
                                  interpretationSections:
                                      interpretationSections,
                                ),
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _primaryColor,
                      foregroundColor: _fillColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _SaveTemplateDialog extends StatefulWidget {
  const _SaveTemplateDialog();

  @override
  State<_SaveTemplateDialog> createState() => _SaveTemplateDialogState();
}

class _SaveTemplateDialogState extends State<_SaveTemplateDialog> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      elevation: 16,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.orange.shade50,
              Colors.white,
              Colors.orange.shade50,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.shade600,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.save_alt,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(height: 16),

              Text(
                'Save as Template',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange.shade700,
                  fontSize: FontSizes.heading1,
                ),
              ),
              const SizedBox(height: 8),

              Text(
                'Create a reusable template from current inputs',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: FontSizes.bodyMedium,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(15),
                      blurRadius: 3,
                      spreadRadius: 1,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'Template Name',
                    hintText: 'e.g., Standard Adult Echo',
                    prefixIcon: Icon(
                      Icons.label_outline,
                      color: Colors.orange.shade600,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    labelStyle: TextStyle(
                      color: Colors.orange.shade600,
                      fontSize: FontSizes.labelMedium,
                    ),
                  ),
                  textCapitalization: TextCapitalization.words,
                ),
              ),
              const SizedBox(height: 16),

              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(15),
                      blurRadius: 3,
                      spreadRadius: 1,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    labelText: 'Description (Optional)',
                    hintText: 'Brief description of this template',
                    prefixIcon: Icon(
                      Icons.description_outlined,
                      color: Colors.orange.shade600,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    labelStyle: TextStyle(
                      color: Colors.orange.shade600,
                      fontSize: FontSizes.labelMedium,
                    ),
                  ),
                  maxLines: 2,
                ),
              ),
              const SizedBox(height: 32),

              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.grey.shade600,
                        side: BorderSide(color: Colors.grey.shade300),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: TextStyle(fontSize: FontSizes.buttonMedium),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        if (_nameController.text.trim().isNotEmpty) {
                          Navigator.of(context).pop({
                            'name': _nameController.text,
                            'description': _descriptionController.text,
                          });
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange.shade600,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 4,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.save, size: 18),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              'Save',
                              style: TextStyle(
                                fontSize: FontSizes.buttonMedium,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
