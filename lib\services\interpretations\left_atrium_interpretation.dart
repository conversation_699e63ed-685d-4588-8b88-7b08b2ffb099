import 'base_interpretation.dart';

class LeftAtriumInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Left atrium assessment was not performed.';
    }

    final patientInfo = data['patientInfo'] ?? {};
    final gender = patientInfo['gender'] as String? ?? 'Not provided';
    final echoParams = data['echoParameters'] ?? {};
    final la = _parseDouble(echoParams['la']);

    if (la != null) {
      final bool isMale = gender.toLowerCase() == 'male';
      final String laSize = _interpretLASize(la, isMale);
      final StringBuffer laParagraph = StringBuffer();

      laParagraph.write(' $laSize in size');

      if (laSize == 'Normal') {
        laParagraph.write('. Showing normal left atrium dimensions.');
      } else if (laSize == 'Mild Increase') {
        laParagraph.write('. Showing mild left atrial enlargement.');
      } else if (laSize == 'Moderate Increase') {
        laParagraph.write('. Showing moderate left atrial enlargement.');
      } else if (laSize == 'Severe Increase') {
        laParagraph.write('. Showing severe left atrial enlargement.');
      }

      if (!laParagraph.toString().endsWith('.')) {
        laParagraph.write('.');
      }

      return laParagraph.toString();
    } else {
      return 'The left atrium size was not measured.';
    }
  }

  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  String _interpretLASize(double la, bool isMale) {
    if (isMale) {
      if (la <= 40) return 'Normal';
      if (la <= 46) return 'Mild Increase';
      if (la <= 51) return 'Moderate Increase';
      return 'Severe Increase';
    } else {
      if (la <= 38) return 'Normal';
      if (la <= 44) return 'Mild Increase';
      if (la <= 49) return 'Moderate Increase';
      return 'Severe Increase';
    }
  }
}
